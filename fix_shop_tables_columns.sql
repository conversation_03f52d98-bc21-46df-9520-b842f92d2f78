-- ====================================================================
-- FIX SHOP TABLES COLUMN NAMES
-- ====================================================================
-- This script fixes the column names in shop_purchases and shop_removals
-- tables to match what the CashShopManager code expects.
--
-- Run this if you already have the tables but with wrong column names
-- and are getting "Unknown column" errors.
-- ====================================================================

USE `not-aion`;

-- Drop existing tables with wrong column names
DROP TABLE IF EXISTS `shop_purchases`;
DROP TABLE IF EXISTS `shop_removals`;

-- Recreate with correct column names
CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `char_id` int(11) DEFAULT NULL,
  `itemId` int(11) NOT NULL,
  `itemCount` int(11) NOT NULL DEFAULT '1',
  `price` bigint(20) NOT NULL DEFAULT '0',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('PENDING','COMPLETED','FAILED') NOT NULL DEFAULT 'PENDING',
  `processed` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  KEY `char_id` (`char_id`),
  KEY `status` (`status`),
  KEY `processed` (`processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `char_id` int(11) DEFAULT NULL,
  `itemUniqueId` int(11) NOT NULL,
  `itemCount` int(11) NOT NULL DEFAULT '1',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reason` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','COMPLETED','FAILED') NOT NULL DEFAULT 'PENDING',
  `processed` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  KEY `char_id` (`char_id`),
  KEY `status` (`status`),
  KEY `processed` (`processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- COLUMN NAMES FIXED!
-- ====================================================================
-- The shop tables now have the correct column names:
-- 
-- shop_purchases:
-- - char_id (not player_id)
-- - itemId (not item_id) 
-- - itemCount (not item_count)
-- - price as bigint(20)
--
-- shop_removals:
-- - char_id (not player_id)
-- - itemUniqueId (not item_id)
-- - itemCount (not item_count)
--
-- Your CashShopManager errors should now be resolved!
-- ====================================================================
