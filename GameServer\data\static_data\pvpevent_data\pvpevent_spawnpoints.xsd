<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	version="1.0">
	<xs:include schemaLocation="../import.xsd" />
	<xs:element name="spawnpoints" type="point3Dgroup"/>
	<xs:complexType name="point3Dgroup">
		<xs:sequence>
			<xs:element ref="import" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="point" type="point3D" minOccurs="0"
				maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="point3D">
		<xs:sequence>
			<xs:element name="x" type="xs:float" default="0"
				minOccurs="1" maxOccurs="1" />
			<xs:element name="y" type="xs:float" default="0"
				minOccurs="1" maxOccurs="1" />
			<xs:element name="z" type="xs:float" default="0"
				minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>