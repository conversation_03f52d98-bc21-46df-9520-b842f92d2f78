/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.WeatherService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.WorldMapType;

/**
 * Admin command allowing to change weathers of the world.
 * 
 * <AUTHOR>
 */

public class Weather extends AdminCommand {

    private final static String RESET = "reset";

    private final static String COMMAND = "weather";

    public Weather() {
        super(COMMAND);
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        // Check restriction level
        if (admin.getAccessLevel() < AdminConfig.COMMAND_WEATHER) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length == 0 || params.length > 2) {
            // Syntax :
            // - //weather poeta 0 -> to set clear sky in this region
            // - //weather reset -> to change randomly all weathers in the world

            PacketSendUtility.sendMessage(admin,
                "syntax //weather <location name> <value>\n//weather reset");
            return;
        }

        String regionName = null;
        int weatherType = -1;

        regionName = new String(params[0]);

        if (params.length == 2) {
            try {
                weatherType = Integer.parseInt(params[1]);
            }
            catch (NumberFormatException e) {
                PacketSendUtility.sendMessage(admin,
                    "weather type parameter need to be an integer [0-8].");
                return;
            }
        }

        if (regionName.equals(RESET)) {
            WeatherService.getInstance().resetWeather();
            return;
        }

        // Retrieving regionId by name
        WorldMapType region = null;
        for (WorldMapType worldMapType : WorldMapType.values()) {
            if (worldMapType.name().toLowerCase().equals(regionName.toLowerCase())) {
                region = worldMapType;
            }
        }

        if (region != null) {
            if (weatherType > -1 && weatherType < 9) {
                WeatherService.getInstance().changeRegionWeather(region.getId(),
                    new Integer(weatherType));
            }
            else {
                PacketSendUtility.sendMessage(admin, "Weather type must be between 0 and 8");
                return;
            }
        }
        else {
            PacketSendUtility.sendMessage(admin, "Region " + regionName + " not found");
            return;
        }
    }
}
