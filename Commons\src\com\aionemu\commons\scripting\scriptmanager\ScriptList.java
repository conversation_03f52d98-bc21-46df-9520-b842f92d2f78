/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.aionemu.commons.scripting.scriptmanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Set;

/**
 * Root element for script descriptors
 *
 * <AUTHOR>
 */
@XmlRootElement(name = "scriptlist")
@XmlAccessorType(XmlAccessType.NONE)
public class ScriptList {
    /**
     * List of Script descriptors
     */
    @XmlElement(name = "scriptinfo", type = ScriptInfo.class)
    private Set<ScriptInfo> scriptInfos;

    /**
     * Returns list of script descriptors
     *
     * @return list of script descriptors
     */
    public Set<ScriptInfo> getScriptInfos() {
        return scriptInfos;
    }

    /**
     * Sets list of script descriptors
     *
     * @param scriptInfos lisft of script descriptors
     */
    public void setScriptInfos(Set<ScriptInfo> scriptInfos) {
        this.scriptInfos = scriptInfos;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        sb.append("ScriptList");
        sb.append("{scriptInfos=").append(scriptInfos);
        sb.append('}');
        return sb.toString();
    }
}
