/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.Race;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;
import gameserver.world.World;

/**
 * Admin announce faction
 * 
 * <AUTHOR>
 */
public class AnnounceFaction extends AdminCommand {
    public AnnounceFaction() {
        super("af");
    }

    @Override
    public void executeCommand(Player admin, final String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ANNOUNCE_FACTION) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility
                .sendMessage(admin, "Syntax: //announcefaction <ely | asmo> <message>");
            return;
        }
        else {
            String message = "";
            Race race = null;

            if ("ely".startsWith(params[0].toLowerCase())) {
                message += "Elyos : ";
                race = Race.ELYOS;
            }
            else if ("asmo".startsWith(params[0].toLowerCase())) {
                message += "Asmodians : ";
                race = Race.ASMODIANS;
            }

            if (race == null) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //announcefaction <ely | asmo> <message>");
                return;
            }

            final Race _race = race;

            // Add with space
            for (int i = 1; i < params.length - 1; i++)
                message += params[i] + " ";

            // Add the last without the end space
            message += params[params.length - 1];

            final String _message = message;
            World.getInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player player) {
                    if (player.getCommonData().getRace() == _race)
                        PacketSendUtility.sendSysMessage(player, _message);
                    return true;
                }
            });
        }
    }
}
