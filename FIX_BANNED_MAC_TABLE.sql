-- ====================================================================
-- FIX BANNED_MAC TABLE - ADD MISSING MAC ADDRESS BANNING TABLE
-- ====================================================================
-- This script creates the missing banned_mac table that is used
-- for banning players by their MAC address (hardware identifier).
-- ====================================================================

-- Change to your database name
USE `not-aion`;

-- Create missing banned_mac table
CREATE TABLE IF NOT EXISTS `banned_mac` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `address` varchar(20) NOT NULL,
  `details` varchar(255) DEFAULT NULL,
  `time_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `address` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Verify the table was created
SELECT 'banned_mac table created successfully' AS status;
DESCRIBE banned_mac;

-- ====================================================================
-- BANNED_MAC TABLE CREATED!
-- ====================================================================
-- The banned_mac table has been created with the following structure:
--
-- - id (int, auto increment primary key)
-- - address (varchar(20), unique - the MAC address like "A8-3B-76-24-C8-24")
-- - details (varchar(255) - reason for ban or admin notes)
-- - time_end (timestamp - when the ban expires, NULL for permanent)
--
-- This table is used by the MySQL5BannedMacDAO to:
-- - Check if a player's MAC address is banned
-- - Store MAC address bans for cheaters/troublemakers
-- - Manage temporary and permanent MAC bans
--
-- Your "Error checking if MAC is banned" error should now be resolved!
-- ====================================================================
