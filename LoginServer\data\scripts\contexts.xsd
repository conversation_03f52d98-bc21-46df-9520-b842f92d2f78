<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
		This file is part of Aion X Emu <aionxemu.com>.

		This is free software: you can redistribute it and/or modify
		it under the terms of the GNU Lesser Public License as published by
		the Free Software Foundation, either version 3 of the License, or
		(at your option) any later version.

		This software is distributed in the hope that it will be useful,
		but WITHOUT ANY WARRANTY; without even the implied warranty of
		MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
		GNU Lesser Public License for more details.

		You should have received a copy of the GNU Lesser Public License
		along with this software.  If not, see <http://www.gnu.org/licenses/>.
-->

<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="scriptinfo" type="scriptInfo"/>

    <xs:element name="scriptlist" type="scriptList"/>

    <xs:complexType name="scriptList">
        <xs:sequence>
            <xs:element ref="scriptinfo" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="scriptInfo">
        <xs:sequence>
            <xs:element ref="scriptinfo" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="library" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="compiler" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="root" type="xs:string" use="required"/>
    </xs:complexType>
</xs:schema>