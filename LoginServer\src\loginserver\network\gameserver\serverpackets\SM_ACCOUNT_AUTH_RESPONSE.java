/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package loginserver.network.gameserver.serverpackets;

import java.nio.ByteBuffer;

import loginserver.model.AccountTime;
import loginserver.network.gameserver.GsConnection;
import loginserver.network.gameserver.GsServerPacket;

/**
 * In this packet LoginServer is answering on GameServer request about valid authentication data and also sends account
 * name of user that is authenticating on GameServer.
 * 
 * <AUTHOR>
 */
public class SM_ACCOUNT_AUTH_RESPONSE extends GsServerPacket{
	/**
	 * Account id
	 */
	private final int		accountId;

	/**
	 * True if account is authenticated.
	 */
	private final boolean	ok;

	/**
	 * account name
	 */
	private final String	accountName;

	/**
	 * Access level
	 */
	private final byte		accessLevel;

	/**
	 * Membership
	 */
	private final byte		membership;

	/**
	 * Constructor.
	 * 
	 * @param accountId
	 * @param ok
	 * @param accountName
	 * @param accessLevel
	 * @param membership
	 */
	public SM_ACCOUNT_AUTH_RESPONSE(int accountId, boolean ok, String accountName, byte accessLevel, byte membership) {
		super(0x01);

		this.accountId = accountId;
		this.ok = ok;
		this.accountName = accountName;
		this.accessLevel = accessLevel;
		this.membership = membership;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void writeImpl(GsConnection con, ByteBuffer buf) {
		writeC(buf, getOpcode());
		writeD(buf, accountId);
		writeC(buf, ok ? 1 : 0);

		if(ok) {
			writeS(buf, accountName);

			AccountTime accountTime = con.getGameServerInfo().getAccountFromGameServer(accountId).getAccountTime();

			writeQ(buf, accountTime.getAccumulatedOnlineTime());
			writeQ(buf, accountTime.getAccumulatedRestTime());
			writeC(buf, accessLevel);
			writeC(buf, membership);
		}
	}
}
