/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.NameChangesDAO;
import gameserver.model.gameobjects.player.Player;

import java.sql.PreparedStatement;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5NameChangesDAO extends NameChangesDAO {
    private static final Logger log = Logger.getLogger(MySQL5NameChangesDAO.class);

    @Override
    public boolean insertNameChange(Player player, String oldName) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT INTO name_changes (player_id, old_name, new_name, date) VALUES (?,?,?,NOW())");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setString(2, oldName);
            ps.setString(3, player.getName());

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error insert name change for player " + player.getObjectId()
                + " with old name " + oldName, e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}
