/*
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package quest.cloister_of_kaisinel;

import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.questEngine.handlers.QuestHandler;
import gameserver.questEngine.model.QuestCookie;
import gameserver.questEngine.model.QuestState;
import gameserver.questEngine.model.QuestStatus;
import gameserver.network.aion.serverpackets.SM_PLAY_MOVIE;
import gameserver.network.aion.serverpackets.SM_DIALOG_WINDOW;
import gameserver.services.QuestService;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 * 
 */
public class _10001BoundOfInggison extends QuestHandler {
    private final static int questId = 10001;

    public _10001BoundOfInggison() {
        super(questId);
    }

    @Override
    public void register() {
        qe.addQuestLvlUp(questId);
        qe.setNpcQuestData(798926).addOnQuestStart(questId); // Outremus
        qe.setNpcQuestData(798926).addOnTalkEvent(questId);
        qe.setNpcQuestData(798600).addOnTalkEvent(questId); // Eremitia
        qe.setNpcQuestData(798513).addOnTalkEvent(questId); // Machiah
        qe.setNpcQuestData(203760).addOnTalkEvent(questId); // Bellia
        qe.setNpcQuestData(203782).addOnTalkEvent(questId); // Jhaelas
        qe.setNpcQuestData(798408).addOnTalkEvent(questId); // Sibylle
        qe.setNpcQuestData(203709).addOnTalkEvent(questId); // Clymene
        qe.setQuestMovieEndIds(501).add(questId);
        qe.addOnEnterWorld(questId);
        qe.addOnDie(questId);
        qe.addOnQuestFinish(questId);
    }

    @Override
    public boolean onDialogEvent(QuestCookie env) {
        final Player player = env.getPlayer();
        int targetId = 0;
        if (env.getVisibleObject() instanceof Npc)
            targetId = ((Npc) env.getVisibleObject()).getNpcId();
        QuestState qs = player.getQuestStateList().getQuestState(questId);

        if (targetId == 798926) {
            if (qs == null) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 1011);
                else
                    return defaultQuestStartDialog(env);
            }
        }

        if (qs == null)
            return false;

        int var = qs.getQuestVarById(0);

        if (qs.getStatus() == QuestStatus.START) {
            if (targetId == 798600 && var == 0) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 1011);
                else if (env.getDialogId() == 10000) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 798513 && var == 1) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 1352);
                else if (env.getDialogId() == 10001) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 203760 && var == 2) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 1693);
                else if (env.getDialogId() == 10002) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 203782 && var == 3) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 2034);
                else if (env.getDialogId() == 10003) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 798408 && var == 4) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 2375);
                else if (env.getDialogId() == 10004) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 203709 && var == 5) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 2716);
                else if (env.getDialogId() == 10005) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 798408 && var == 6) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 3057);
                else if (env.getDialogId() == 10006) {
                    qs.setQuestVar(++var);
                    updateQuestStatus(env);
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(env
                        .getVisibleObject().getObjectId(), 10));
                    return true;
                }
                else
                    return defaultQuestStartDialog(env);
            }
            else if (targetId == 798408 && var == 7) {
                if (env.getDialogId() == 25)
                    return sendQuestDialog(env, 3398);
                else if (env.getDialogId() == 10255) {
                    PacketSendUtility.sendPacket(player, new SM_PLAY_MOVIE(0, 501));
                    return true;
                }
            }

        }
        else if (qs.getStatus() == QuestStatus.REWARD) {
            if (env.getTargetId() == 798926) {
                if (env.getDialogId() == -1)
                    return sendQuestDialog(env, 10002);
                else if (env.getDialogId() == 17) {
                    int[] ids = { 10020, 10021, 10022, 10023, 10024, 10025, 10026 };
                    for (int id : ids)
                        QuestService.startQuest(
                            new QuestCookie(env.getVisibleObject(), env.getPlayer(), id, env
                                .getDialogId()), QuestStatus.LOCKED);
                }
                return defaultQuestEndDialog(env);
            }
        }
        return false;
    }

    @Override
    public boolean onMovieEndEvent(QuestCookie env, int movieId) {
        if (movieId != 501)
            return false;

        Player player = env.getPlayer();
        QuestState qs = player.getQuestStateList().getQuestState(questId);
        if (qs == null || qs.getStatus() != QuestStatus.START
            || qs.getQuestVars().getQuestVars() != 7)
            return false;
        TeleportService.teleportTo(player, 210050000, 1, 1321, 257, 592, (byte) 20, 0);
        qs.setStatus(QuestStatus.REWARD);
        updateQuestStatus(env);
        return true;
    }

    @Override
    public boolean onLvlUpEvent(QuestCookie env) {
        final Player player = env.getPlayer();
        final QuestState qs = player.getQuestStateList().getQuestState(questId);
        if (qs != null)
            return false;

        QuestState qs2 = player.getQuestStateList().getQuestState(10000);
        if (qs2 == null || qs2.getStatus() != QuestStatus.COMPLETE)
            return false;
        env.setQuestId(questId);
        QuestService.startQuest(env, QuestStatus.START);
        return true;
    }
}
