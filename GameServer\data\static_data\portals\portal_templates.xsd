<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:include schemaLocation="../import.xsd"/>
    <xs:include schemaLocation="../global_types.xsd"/>

    <xs:element name="portal_templates">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="portal" type="Portal" minOccurs="0"
                            maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Portal">
        <xs:sequence>
            <xs:element name="entrypoint" type="EntryPoint" minOccurs="0"
                        maxOccurs="2"/>
            <xs:element name="exitpoint" type="ExitPoint" minOccurs="1"
                        maxOccurs="2"/>
            <xs:element name="portalitem" type="PortalItem" minOccurs="0"
                        maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="race" type="Race"/>
        <xs:attribute name="group" type="xs:boolean"/>
        <xs:attribute name="maxlevel" type="xs:int"/>
        <xs:attribute name="minlevel" type="xs:int"/>
        <xs:attribute name="instance" type="xs:boolean"/>
        <xs:attribute name="name" type="xs:string"/>
        <xs:attribute name="npcid" type="xs:int"/>
        <xs:attribute name="titleid" type="xs:int"/>
   		  <xs:attribute name="usedelay" type="xs:int"/>
	</xs:complexType>

    <xs:complexType name="EntryPoint">
        <xs:attribute name="mapid" type="xs:int"/>
        <xs:attribute name="x" type="xs:float"/>
        <xs:attribute name="y" type="xs:float"/>
        <xs:attribute name="z" type="xs:float"/>
        <xs:attribute name="race" type="Race"/>
    </xs:complexType>

    <xs:complexType name="ExitPoint">
        <xs:attribute name="mapid" type="xs:int"/>
        <xs:attribute name="x" type="xs:float"/>
        <xs:attribute name="y" type="xs:float"/>
        <xs:attribute name="z" type="xs:float"/>
        <xs:attribute name="race" type="Race"/>
    </xs:complexType>

    <xs:complexType name="PortalItem">
        <xs:attribute name="id" type="xs:int"/>
        <xs:attribute name="itemid" type="xs:int"/>
        <xs:attribute name="quantity" type="xs:int"/>
    </xs:complexType>

</xs:schema>

