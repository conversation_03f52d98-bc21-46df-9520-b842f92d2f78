/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.PlayerMotionListDAO;
import gameserver.model.gameobjects.player.MotionList;
import gameserver.model.gameobjects.player.MotionList.PlayerMotion;
import gameserver.model.gameobjects.player.Player;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5PlayerMotionListDAO extends PlayerMotionListDAO {
    private static final String LOAD_QUERY = "SELECT `motion_id`, `motion_expires_time`, `motion_date` FROM `player_motions` WHERE `player_id`=?";
    private static final String INSERT_QUERY = "INSERT IGNORE INTO `player_motions`(`player_id`, `motion_id`, `motion_expires_time`, `motion_date`) VALUES (?,?,?,?)";
    private static final String CHECK_QUERY = "SELECT `motion_id` FROM `player_motions` WHERE `player_id`=? AND `motion_id`=?";
    private static final String DELETE_QUERY = "DELETE FROM `player_motions` WHERE `player_id` = ? AND `motion_id` = ?";

    private static final Logger log = Logger.getLogger(MySQL5PlayerMotionListDAO.class);

    @Override
    public MotionList loadMotionList(final int playerId) {
        final MotionList ml = new MotionList();

        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(LOAD_QUERY);
            stmt.setInt(1, playerId);
            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                int id = rset.getInt("motion_id");
                long title_date = rset.getTimestamp("motion_date").getTime();
                long title_expires_time = rset.getLong("motion_expires_time");

                ml.addMotion(id, title_date, title_expires_time);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return ml;
    }

    @Override
    public boolean storeMotions(Player player) {
        final int playerId = player.getObjectId();

        for (final PlayerMotion m : player.getMotionList().getMotions()) {

            Connection con = null;
            try {
                con = DatabaseFactory.getConnection();

                PreparedStatement stmt = con.prepareStatement(INSERT_QUERY);
                stmt.setInt(1, playerId);
                stmt.setInt(2, m.getMotionId());
                stmt.setLong(3, m.getExpireTime());
                stmt.setTimestamp(4, new Timestamp(m.getDate()));

                stmt.execute();
            }
            catch (Exception e) {
                log.error(e);
            }
            finally {
                DatabaseFactory.close(con);
            }

        }
        return true;
    }

    @Override
    public void removeMotion(int playerId, int motionId) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt2 = con.prepareStatement(DELETE_QUERY);
            stmt2.setInt(1, playerId);
            stmt2.setInt(2, motionId);
            stmt2.execute();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }

}
