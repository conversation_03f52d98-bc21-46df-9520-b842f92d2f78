/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class WrapCommand extends AdminCommand {

    public WrapCommand() {
        super("wrap");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < 5) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //wrap <item>");
            return;
        }

        int itemId = Integer.parseInt(params[0]);

        List<Item> items = admin.getInventory().getAllItemsByItemId(itemId);

        if (items.isEmpty()) {
            PacketSendUtility.sendMessage(admin, "You do not have the item in your inventory.");
            return;
        }

        Item selectedItem = null;

        for (Item item : items) {
            if (item.isWrapped())
                continue;

            selectedItem = item;
            break;
        }

        if (selectedItem == null) {
            PacketSendUtility.sendMessage(admin, "Couldn't find a suitable item.");
            return;
        }

        selectedItem.setWrapped(true);

        PacketSendUtility.sendPacket(admin, new SM_UPDATE_ITEM(selectedItem, 1));

        admin.getInventory().setPersistentState(PersistentState.UPDATE_REQUIRED);

        PacketSendUtility.sendMessage(admin, "[item:" + selectedItem.getItemId()
            + "] has been wrapped.");
    }
}
