# Aion X Emu Build Script for PowerShell
# This script builds all server components with Java 1.6

Write-Host "========================================" -ForegroundColor Green
Write-Host "Aion X Emu Build Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set Java 1.6 environment
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.6.0_45"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host "Using Java 1.6 from: $env:JAVA_HOME" -ForegroundColor Yellow

# Function to build a component
function Build-Component {
    param(
        [string]$ComponentName,
        [string]$ComponentPath
    )
    
    Write-Host "`nBuilding $ComponentName..." -ForegroundColor Cyan
    
    Push-Location $ComponentPath
    try {
        & "..\Tools\Ant\bin\ant.bat" clean dist
        if ($LASTEXITCODE -eq 0) {
            Write-Host "$ComponentName build successful!" -ForegroundColor Green
        } else {
            Write-Host "$ComponentName build failed!" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error building $ComponentName`: $_" -ForegroundColor Red
        return $false
    } finally {
        Pop-Location
    }
    return $true
}

# Build all components
$components = @(
    @{Name="Commons"; Path="Commons"},
    @{Name="GameServer"; Path="GameServer"},
    @{Name="LoginServer"; Path="LoginServer"},
    @{Name="ChatServer"; Path="ChatServer"}
)

$allSuccess = $true

foreach ($component in $components) {
    $success = Build-Component -ComponentName $component.Name -ComponentPath $component.Path
    if (-not $success) {
        $allSuccess = $false
    }
}

Write-Host "`n========================================" -ForegroundColor Green
if ($allSuccess) {
    Write-Host "All components built successfully!" -ForegroundColor Green
} else {
    Write-Host "Some components failed to build!" -ForegroundColor Red
}
Write-Host "========================================" -ForegroundColor Green

# Pause to see results
Read-Host "Press Enter to continue..."
