/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package mysql5;

import gameserver.dao.MightDAO;
import gameserver.model.gameobjects.player.Player;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * 
 * <AUTHOR>
 */
public class MySQL5MightDAO extends MightDAO {
    private static final Logger log = Logger.getLogger(MySQL5MightDAO.class);

    public boolean addMight(Player player, int might) {
        return addMight(player.getPlayerAccount().getId(), might);
    }

    public boolean addMight(int accountId, int might) {
        Connection con = null;

        /*
         * if (this.checkExists(accountId)) { try { con = DatabaseFactory.getConnection(); PreparedStatement stmt =
         * con.prepareStatement("UPDATE might SET might = might + ? WHERE account_id = ?"); stmt.setInt(1, might);
         * stmt.setInt(2, accountId); stmt.execute(); return true; } catch (SQLException e) { log.error(e); return
         * false; } finally { DatabaseFactory.close(con); } } else {
         */
        try {
            con = DatabaseFactory.getConnection();
            // PreparedStatement stmt = con.prepareStatement("INSERT INTO might (account_id, might) VALUES (?, ?)");
            // stmt.setInt(1, accountId);
            // stmt.setInt(2, might);
            PreparedStatement stmt = con
                .prepareStatement("INSERT INTO might (account_id, might) VALUES (?, ?) ON DUPLICATE KEY UPDATE might = might + ?");
            stmt.setInt(1, accountId);
            stmt.setInt(2, might);
            stmt.setInt(3, might);
            stmt.execute();

            return true;
        }
        catch (SQLException e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        // }
    }

    public int getMight(Player player) {
        return getMight(player.getPlayerAccount().getId());
    }

    public int getMight(int accountId) {
        Connection con = null;
        int might = 0;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT might FROM might WHERE account_id = ?");

            stmt.setInt(1, accountId);
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                might = rset.getInt("might");
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return might;
    }

    public boolean setMight(Player player, int might) {
        Connection con = null;

        /*
         * if (this.checkExists(player)) { try { con = DatabaseFactory.getConnection(); PreparedStatement stmt = con
         * .prepareStatement("UPDATE might SET might = ? WHERE account_id = ?"); stmt.setInt(1, might); stmt.setInt(2,
         * this.getAccountId(player)); stmt.execute(); return true; } catch (SQLException e) { log.error(e); return
         * false; } finally { DatabaseFactory.close(con); } } else {
         */
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("REPLACE INTO might (account_id, might) VALUES (?, ?)");
            stmt.setInt(1, player.getPlayerAccount().getId());
            stmt.setInt(2, might);
            stmt.execute();

            return true;
        }
        catch (SQLException e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        // }
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}