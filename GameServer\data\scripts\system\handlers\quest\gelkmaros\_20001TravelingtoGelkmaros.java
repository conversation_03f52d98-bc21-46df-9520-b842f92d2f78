/*
 * This file is part of Aion X EMU <aionxemu.com>.
 *
 * This is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package quest.gelkmaros;

import gameserver.model.gameobjects.player.Player;
import gameserver.questEngine.handlers.QuestHandler;
import gameserver.questEngine.model.QuestCookie;
import gameserver.questEngine.model.QuestState;
import gameserver.questEngine.model.QuestStatus;
import gameserver.services.QuestService;
import gameserver.services.TeleportService;

/**
 * <AUTHOR>
 */
public class _20001TravelingtoGelkmaros extends QuestHandler {
    private final static int questId = 20001;
    private final static int[] npc_ids = { 798800, 798409, 204202, 204073, 204283, 799225 };

    public _20001TravelingtoGelkmaros() {
        super(questId);
    }

    @Override
    public void register() {
        qe.addQuestLvlUp(questId);
        for (int npc_id : npc_ids)
            qe.setNpcQuestData(npc_id).addOnTalkEvent(questId);
    }

    @Override
    public boolean onLvlUpEvent(QuestCookie env) {
        return defaultQuestOnLvlUpEvent(env, 20000);
    }

    @Override
    public boolean onDialogEvent(QuestCookie env) {
        if (!super.defaultQuestOnDialogInitStart(env))
            return false;

        Player player = env.getPlayer();
        QuestState qs = player.getQuestStateList().getQuestState(questId);
        int var = qs.getQuestVarById(0);

        if (qs.getStatus() == QuestStatus.START) {
            switch (env.getTargetId()) {
                case 798800:
                    switch (env.getDialogId()) {
                        case 26:
                            if (var == 0)
                                return sendQuestDialog(env, 1011);
                        case 10000:
                            return defaultCloseDialog(env, 0, 1);
                    }
                case 798409:
                    switch (env.getDialogId()) {
                        case 26:
                            if (var == 1)
                                return sendQuestDialog(env, 1352);
                            else if (var == 5)
                                return sendQuestDialog(env, 2716);
                        case 10001:
                            return defaultCloseDialog(env, 1, 2);
                        case 10255:
                            if (defaultCloseDialog(env, 5, 0, true, false)) {
                                TeleportService.teleportTo(player, 220070000, 1, 1867.56f,
                                    2748.76f, 531.971f, (byte) 77, 0);
                                return true;
                            }
                    }
                case 204202:
                    switch (env.getDialogId()) {
                        case 26:
                            if (var == 2)
                                return sendQuestDialog(env, 1694);
                        case 10002:
                            return defaultCloseDialog(env, 2, 3);
                    }
                case 204073:
                    switch (env.getDialogId()) {
                        case 26:
                            if (var == 3)
                                return sendQuestDialog(env, 2034);
                        case 10003:
                            return defaultCloseDialog(env, 3, 4);
                    }
                case 204283:
                    switch (env.getDialogId()) {
                        case 26:
                            if (var == 4)
                                return sendQuestDialog(env, 2375);
                        case 10004:
                            return defaultCloseDialog(env, 4, 5);
                    }
            }
        }
        else if (qs.getStatus() == QuestStatus.REWARD) {
            if (env.getTargetId() == 799225) {
                if (env.getDialogId() == -1)
                    return sendQuestDialog(env, 10002);
                else if (env.getDialogId() == 17) {
                    int[] ids = { 20020, 20021, 20022, 20023, 20024, 20025, 20026 };
                    for (int id : ids)
                        QuestService.startQuest(
                            new QuestCookie(env.getVisibleObject(), env.getPlayer(), id, env
                                .getDialogId()), QuestStatus.LOCKED);
                }
                return defaultQuestEndDialog(env);
            }
        }
        return false;
    }
}
