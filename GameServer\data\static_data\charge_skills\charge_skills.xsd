<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    <xs:element name="charge_skills">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="charge_skill" type="ChargeSkillTemplate" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ChargeSkillTemplate">
		<xs:sequence>
			<xs:element name="charge" type="ChargeTemplate" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="min_charge" type="xs:int" use="required"/>
	</xs:complexType>
	<xs:complexType name="ChargeTemplate">
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="time" type="xs:int" use="required"/>
	</xs:complexType>
</xs:schema>