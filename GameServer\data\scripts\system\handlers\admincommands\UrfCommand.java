/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.URFService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 */
public class UrfCommand extends AdminCommand {
    public UrfCommand() {
        super("urf");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SPAWN) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility
                .sendMessage(admin, "Syntax: //urf <cooldown | mana | effectduration>");
            return;
        }
        else if ("cooldown".equalsIgnoreCase(params[0])) {
            if (params.length < 2 || Integer.parseInt(params[1]) < 1) {
                PacketSendUtility
                    .sendMessage(
                        admin,
                        "URF COOLDOWN: "
                            + URFService.getInstance().getCooldown()
                            * 100
                            + "\nSyntax: //urf cooldown <1-100-200> -- 1 is lowest CD, 100 is normal, 200 is double");
                return;
            }

            float cooldown = Integer.parseInt(params[1]) * 0.01f;
            URFService.getInstance().setCooldown(cooldown);

            PacketSendUtility.sendMessage(admin, "Changed URF COOLDOWN to "
                + URFService.getInstance().getCooldown() * 100);
        }
        else if ("mana".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "URF MANA: "
                    + (URFService.getInstance().isManaDisabled() ? "DISABLED" : "ENABLED")
                    + "\nSyntax: //urf mana <enable | disable> -- enable or disable mana costs");
                return;
            }

            if ("enable".equalsIgnoreCase(params[1])) {
                URFService.getInstance().setManaDisabled(false);
            }
            else if ("disable".equalsIgnoreCase(params[1])) {
                URFService.getInstance().setManaDisabled(true);
            }
            else {
                PacketSendUtility.sendMessage(admin, "Error! Please either enable or disable.");
                return;
            }

            PacketSendUtility.sendMessage(admin, "Changed URF MANA to "
                + (URFService.getInstance().isManaDisabled() ? "DISABLED" : "ENABLED"));
        }
        else if ("effectduration".equalsIgnoreCase(params[0])) {
            if (params.length < 2 || Integer.parseInt(params[1]) < 1) {
                PacketSendUtility
                    .sendMessage(
                        admin,
                        "URF EFFECT DURATION: "
                            + URFService.getInstance().getEffectDuration()
                            * 100
                            + "\nSyntax: //urf effectduration <1-100-200> -- 1 is lowest duration, 100 is normal, 200 is double");
                return;
            }

            float effectDuration = Integer.parseInt(params[1]) * 0.01f;
            URFService.getInstance().setEffectDuration(effectDuration);

            PacketSendUtility.sendMessage(admin, "Changed URF EFFECT DURATION to "
                + URFService.getInstance().getEffectDuration() * 100);
        }
    }
}
