/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.PolymorphService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Polymorph extends AdminCommand {
    public Polymorph() {
        super("polymorph");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_MORPH) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (params.length == 1 && "cancel".startsWith(params[0])) {
            if (!(admin.getTarget() instanceof Player)) {
                PacketSendUtility.sendMessage(admin, "Please select a player.");
                return;
            }

            PolymorphService.morphPlayer((Player) admin.getTarget(), 0, 0, 1);
        }
        else if (params.length == 2 && "turret".startsWith(params[0])) {
            if (!(admin.getTarget() instanceof Player)) {
                PacketSendUtility.sendMessage(admin, "Please select a player.");
                return;
            }

            PolymorphService.makeTurret((Player) admin.getTarget(),
                1000 * Integer.parseInt(params[1]));
        }
        else if (params.length < 3) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //polymorph <transformId> <polymorphId> <polymorphState> -- morphs target"
                    + "\nSyntax: OR //polymorph cancel"
                    + "\nSyntax: OR //polymorph turret <duration> -- in seconds");
            return;
        }

        if (!(admin.getTarget() instanceof Player)) {
            PacketSendUtility.sendMessage(admin, "Please select a player.");
            return;
        }

        int transformId = Integer.parseInt(params[0]);
        int polymorphId = Integer.parseInt(params[1]);
        int polymorphState = Integer.parseInt(params[2]);

        PolymorphService.morphPlayer((Player) admin.getTarget(), transformId, polymorphId,
            polymorphState);
    }
}
