/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_CHANNEL_INFO;
import gameserver.network.aion.serverpackets.SM_PLAYER_SPAWN;
import gameserver.services.HousingService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * <AUTHOR>
 * 
 */
public class Housing extends AdminCommand {

    public Housing() {
        super("housing");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SPAWN) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //housing <houseType>");
            return;
        }

        int houseType = Integer.parseInt(params[0]);

        HousingService.getInstance().setDefaultHouseType(houseType);

        PacketSendUtility.sendMessage(admin, "Default house type changed to " + houseType);

        admin.getKnownList().clear();
        World.getInstance().despawn(admin);

        PacketSendUtility.sendPacket(admin, new SM_CHANNEL_INFO(admin.getPosition()));
        PacketSendUtility.sendPacket(admin, new SM_PLAYER_SPAWN(admin));
    }
}
