-- Database fixes for missing visual_title column and player_settings table
-- Run this script on your existing database to fix the errors

USE `not-aion`;

-- Add missing visual_title column to players table
ALTER TABLE `players` ADD COLUMN `visual_title` int(3) NOT NULL DEFAULT '-1' AFTER `title_id`;

-- Create missing player_settings table
CREATE TABLE IF NOT EXISTS `player_settings` (
  `player_id` int(11) NOT NULL,
  `settings_type` int(11) NOT NULL,
  `settings` blob,
  PRIMARY KEY (`player_id`,`settings_type`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Verify the changes
DESCRIBE `players`;
DESCRIBE `player_settings`;
