-- Database fixes for missing visual_title column and player_settings table
-- Run this script on your existing database to fix the errors

USE `not-aion`;

-- Add missing visual_title column to players table
ALTER TABLE `players` ADD COLUMN `visual_title` int(3) NOT NULL DEFAULT '-1' AFTER `title_id`;

-- Create missing player_settings table
CREATE TABLE IF NOT EXISTS `player_settings` (
  `player_id` int(11) NOT NULL,
  `settings_type` int(11) NOT NULL,
  `settings` blob,
  PRIMARY KEY (`player_id`,`settings_type`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create missing player_clients table
CREATE TABLE IF NOT EXISTS `player_clients` (
  `account_id` int(11) NOT NULL,
  `mac` varchar(50) NOT NULL DEFAULT '',
  `version` varchar(20) NOT NULL DEFAULT '',
  PRIMARY KEY (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create missing player_motions table
CREATE TABLE IF NOT EXISTS `player_motions` (
  `player_id` int(11) NOT NULL,
  `motion_id` int(11) NOT NULL,
  `motion_expires_time` bigint(20) NOT NULL DEFAULT '0',
  `motion_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`,`motion_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create missing motions table
CREATE TABLE IF NOT EXISTS `motions` (
  `player_id` int(11) NOT NULL,
  `motion_idle` int(11) NOT NULL DEFAULT '0',
  `motion_run` int(11) NOT NULL DEFAULT '0',
  `motion_jump` int(11) NOT NULL DEFAULT '0',
  `motion_rest` int(11) NOT NULL DEFAULT '0',
  `motion_shop` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`player_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Verify the changes
DESCRIBE `players`;
DESCRIBE `player_settings`;
DESCRIBE `player_clients`;
DESCRIBE `player_motions`;
DESCRIBE `motions`;
