/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package loginserver.network.aion.clientpackets;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.GeneralSecurityException;

import javax.crypto.Cipher;

import loginserver.dao.AccessLogDAO;
import loginserver.network.aion.AionAuthResponse;
import loginserver.network.aion.AionClientPacket;
import loginserver.network.aion.AionConnection;
import loginserver.network.aion.SecurityCode;
import loginserver.network.aion.SessionKey;
import loginserver.network.aion.serverpackets.SM_LOGIN_FAIL;
import loginserver.network.aion.serverpackets.SM_LOGIN_OK;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class CM_PINCODE extends AionClientPacket{
	private static final Logger	log	= Logger.getLogger(CM_PINCODE.class);

	/**
	 * accountId is part of session key - its used for security purposes
	 */
	private int					accountId;

	private byte[]				data;

	/**
	 * Constructs new instance of <tt>CM_PINCODE</tt> packet.
	 * 
	 * @param buf
	 *            packet data
	 * @param client
	 *            client
	 */
	public CM_PINCODE(ByteBuffer buf, AionConnection client) {
		super(buf, client, 0x06);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void readImpl() {
		accountId = readD();

		data = readB(getRemainingBytes());
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void runImpl() {
		AionConnection con = getConnection();
		SessionKey key = con.getSessionKey();
		SecurityCode code = con.getSecurityCode();

		if(code == null)
			return;

		byte[] decrypted;
		try {
			Cipher rsaCipher = Cipher.getInstance("RSA/ECB/nopadding");
			rsaCipher.init(Cipher.DECRYPT_MODE, getConnection().getRSAPrivateKey());
			decrypted = rsaCipher.doFinal(data, 0, 128);
		}
		catch(GeneralSecurityException e) {
			log.warn("Error while decripting data on user auth." + e, e);
			sendPacket(new SM_LOGIN_FAIL(AionAuthResponse.SYSTEM_ERROR));
			return;
		}

		ByteBuffer buf = ByteBuffer.wrap(decrypted);

		buf.order(ByteOrder.LITTLE_ENDIAN);
		buf.position(124);

		int pinCode = buf.getInt();

		if(code.accountId == accountId && code.pin == pinCode) {
			con.setSecurityCode(null);
			con.sendPacket(new SM_LOGIN_OK(key));
		}
		else {
			con.close(new SM_LOGIN_FAIL(AionAuthResponse.INVALID_SECURITY_CODE), true);
			
			DAOManager.getDAO(AccessLogDAO.class).logAccess(con.getAccount().getName(),
				AionAuthResponse.INVALID_PASSWORD, con.getIP());
		}
	}

	/*final protected static char[]	hexArray	= "0123456789ABCDEF".toCharArray();

	public static String bytesToHex(byte[] bytes) {
		char[] hexChars = new char[bytes.length * 2];
		for(int j = 0; j < bytes.length; j++) {
			int v = bytes[j] & 0xFF;
			hexChars[j * 2] = hexArray[v >>> 4];
			hexChars[j * 2 + 1] = hexArray[v & 0x0F];
		}
		return new String(hexChars);
	}*/
}
