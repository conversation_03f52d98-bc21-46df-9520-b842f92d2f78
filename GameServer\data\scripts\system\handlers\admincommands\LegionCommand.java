/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.legion.Legion;
import gameserver.model.legion.LegionHistoryType;
import gameserver.model.legion.LegionMember;
import gameserver.model.legion.LegionMemberEx;
import gameserver.model.legion.LegionRank;
import gameserver.network.aion.serverpackets.SM_LEGION_UPDATE_MEMBER;
import gameserver.services.LegionService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */

public class LegionCommand extends AdminCommand {

    /**
     * The constructor of Legion Command
     */
    public LegionCommand() {
        super("legion");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_LEGION) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "syntax //legion <disband | setlevel | setpoints | setname | members | setbg | resetcache> <legion name> <value>");
            return;
        }

        LegionService legionService = LegionService.getInstance();
        Legion legion = legionService.getLegion(params[1].toLowerCase());
        if (legion == null) {
            PacketSendUtility.sendMessage(admin, "The " + params[1].toLowerCase()
                + " legion does not exist.");
            return;
        }

        if (params[0].toLowerCase().equals("disband")) {
            legionService.disbandLegion(legion);
            PacketSendUtility.sendMessage(admin, "The following legion has been disbanded: "
                + legion.getLegionName());
        }
        else if (params[0].toLowerCase().equals("setlevel")) {
            int newLevel = Integer.parseInt(params[2]);

            if (newLevel < 1 || newLevel > 8) {
                PacketSendUtility.sendMessage(admin, "Please use a valid legion level. (1 - 8)");
                return;
            }
            else if (legion.getLegionLevel() == newLevel) {
                PacketSendUtility.sendMessage(admin, "Level of legion already is " + newLevel);
                return;
            }

            legionService.changeLevel(legion, newLevel, true);
            PacketSendUtility.sendMessage(admin, "The " + legion.getLegionName()
                + " legion has been leveled up to level " + newLevel);
        }
        else if (params[0].toLowerCase().equals("setpoints")) {
            int newPoints = Integer.parseInt(params[2]);

            if (newPoints < 0 || newPoints > 2000000000) {
                PacketSendUtility.sendMessage(admin,
                    "Please use valid points amount. (0 - 2.000.000.000)");
                return;
            }
            else if (legion.getContributionPoints() == newPoints) {
                PacketSendUtility.sendMessage(admin, "Contribution Points of legion already is "
                    + newPoints);
                return;
            }

            legionService.setContributionPoints(legion, newPoints, true);
            PacketSendUtility.sendMessage(admin, "The " + legion.getLegionName()
                + " legion points have been changed to " + newPoints);
        }
        else if (params[0].toLowerCase().equals("setname")) {
            String newLegionName = params[2];

            if (!legionService.isValidName(newLegionName)) {
                PacketSendUtility.sendMessage(admin, "Please use a valid legion name!");
                return;
            }
            else if (legion.getLegionName().toLowerCase() == newLegionName.toLowerCase()) {
                PacketSendUtility.sendMessage(admin, "Name of legion already is " + newLegionName);
                return;
            }

            legionService.setLegionName(legion, newLegionName, true);
            PacketSendUtility.sendMessage(admin, "The " + legion.getLegionName()
                + " legion's name has been changed to " + newLegionName);
        }
        else if (params[0].equalsIgnoreCase("members")) {
            ArrayList<LegionMemberEx> legionmemblist = LegionService.getInstance()
                .loadLegionMemberExList(legion);

            StringBuilder sb = new StringBuilder();

            sb.append("Legion " + legion.getLegionName() + " (" + legion.getLegionLevel()
                + ") memberlist:");

            for (Iterator<LegionMemberEx> it = legionmemblist.iterator(); it.hasNext();) {
                LegionMemberEx member = it.next();
                sb.append("\n- " + member.getName() + " (" + member.getRank().name() + "): "
                    + (member.isOnline() ? "online" : "not online"));
            }

            PacketSendUtility.sendMessage(admin, sb.toString());
        }
        else if (params[0].equalsIgnoreCase("setbg")) {
            String newBg = params[2];
            Player targetPlayer = World.getInstance().findPlayer(Util.convertName(newBg));

            if (targetPlayer == null || targetPlayer.getLegion() == null
                || targetPlayer.getLegion().getLegionId() != legion.getLegionId()) {
                PacketSendUtility.sendMessage(admin,
                    "The new Brigade General is either not online, not in a legion or not in the "
                        + legion.getLegionName() + " legion.");
                return;
            }

            List<LegionMemberEx> members = LegionService.getInstance().loadLegionMemberExList(
                legion);

            for (LegionMemberEx member : members) {
                if (member.getRank() == LegionRank.BRIGADE_GENERAL) {
                    // Demote Brigade General to Centurion
                    member.setRank(LegionRank.CENTURION);
                    LegionService.getInstance().storeLegionMember(member);
                }
            }

            // Promote member to Brigade General
            LegionMember legionMember = targetPlayer.getLegionMember();

            legionMember.setRank(LegionRank.BRIGADE_GENERAL);

            PacketSendUtility.broadcastPacketToLegion(legion, new SM_LEGION_UPDATE_MEMBER(
                targetPlayer, 1300273, targetPlayer.getName()));
            LegionService.getInstance().addHistory(legion, targetPlayer.getName(),
                LegionHistoryType.APPOINTED);

            PacketSendUtility.sendMessage(admin,
                "Legion " + params[1] + " now has " + targetPlayer.getName()
                    + " as Brigade General.");
        }
        else if (params[0].equalsIgnoreCase("resetcache")) {
            legionService.clearLegionCache(legion);
            PacketSendUtility.sendMessage(admin, "Cleared cache of legion " + params[1]);
        }
    }
}
