@echo off
title Building GameServer - Aion X Emu
echo ========================================
echo Building GameServer...
echo ========================================

REM Set Java 1.6 environment
set JAVA_HOME=C:\Program Files\Java\jdk1.6.0_45
set PATH=%JAVA_HOME%\bin;%PATH%

echo Using Java 1.6 from: %JAVA_HOME%
echo.

REM Change to GameServer directory and build
cd /d "%~dp0"
..\Tools\Ant\bin\ant.bat clean dist

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo GameServer build completed successfully!
    echo ========================================
) else (
    echo.
    echo ========================================
    echo GameServer build FAILED!
    echo ========================================
)

pause
