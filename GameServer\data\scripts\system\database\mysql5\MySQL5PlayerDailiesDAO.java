/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.PlayerDailiesDAO;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService.PvPMode;

import java.sql.PreparedStatement;
import java.sql.ResultSet;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5PlayerDailiesDAO extends PlayerDailiesDAO {
    private static final Logger log = Logger.getLogger(MySQL5PlayerDailiesDAO.class);

    @Override
    public int getDailyValue(Player player, PvPMode pvpMode) {
        PreparedStatement ps = DB
            .prepareStatement("SELECT value FROM player_dailies WHERE player_id = ? AND mode = ?");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setInt(2, pvpMode.getId());

            ResultSet rs = ps.executeQuery();

            if (rs.next())
                return rs.getInt("value");
        }
        catch (Exception e) {
            log.error("Error getting Daily value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return 0;
    }

    @Override
    public boolean addDailyValue(Player player, PvPMode pvpMode, int value) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT INTO player_dailies (player_id, mode, value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE value = value + ?");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setInt(2, pvpMode.getId());
            ps.setInt(3, value);
            ps.setInt(4, value);

            return ps.executeUpdate() > 0;
        }
        catch (Exception e) {
            log.error("Error adding Daily value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return false;
    }

    @Override
    public int getDailyValue(int playerObjId, PvPMode pvpMode) {
        PreparedStatement ps = DB
            .prepareStatement("SELECT value FROM player_dailies WHERE player_id = ? AND mode = ?");

        try {
            ps.setInt(1, playerObjId);
            ps.setInt(2, pvpMode.getId());

            ResultSet rs = ps.executeQuery();

            if (rs.next())
                return rs.getInt("value");
        }
        catch (Exception e) {
            log.error("Error getting Daily value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return 0;
    }

    @Override
    public boolean resetPlayerDailies(Player player) {
        PreparedStatement ps = DB
            .prepareStatement("DELETE FROM player_dailies WHERE player_id = ?");

        try {
            ps.setInt(1, player.getObjectId());

            return ps.executeUpdate() > 0;
        }
        catch (Exception e) {
            log.error("Error resetting player " + player.getName() + " Daily value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return false;
    }

    @Override
    public boolean resetAllDailies() {
        PreparedStatement ps = DB.prepareStatement("DELETE FROM player_dailies");

        try {
            return ps.executeUpdate() > 0;
        }
        catch (Exception e) {
            log.error("Error resetting all Daily values: ", e);
        }
        finally {
            DB.close(ps);
        }

        return false;
    }

    @Override
    public boolean resetCompletedDailies(PvPMode... pvpModes) {
        for (PvPMode pvpMode : pvpModes) {
            PreparedStatement ps = DB
                .prepareStatement("DELETE FROM player_dailies WHERE mode = ? AND value >= ?");

            try {
                ps.setInt(1, pvpMode.getId());
                ps.setInt(2, pvpMode.getCap());

                ps.executeUpdate();
            }
            catch (Exception e) {
                log.error("Error resetting completed Daily values: ", e);
            }
            finally {
                DB.close(ps);
            }
        }

        return true;
    }

    @Override
    public boolean resetPlayerCompletedDailies(Player player, PvPMode... pvpModes) {
        for (PvPMode pvpMode : pvpModes) {
            PreparedStatement ps = DB
                .prepareStatement("DELETE FROM player_dailies WHERE mode = ? AND value >= ? AND player_id = ?");

            try {
                ps.setInt(1, pvpMode.getId());
                ps.setInt(2, pvpMode.getCap());
                ps.setInt(3, player.getObjectId());

                ps.executeUpdate();
            }
            catch (Exception e) {
                log.error("Error resetting completed Daily values: ", e);
            }
            finally {
                DB.close(ps);
            }
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}
