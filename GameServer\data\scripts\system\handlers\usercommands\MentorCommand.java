/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.network.aion.serverpackets.SM_TARGET_SELECTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 */
public class MentorCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    private static int MEMBERSHIP_REQ = 1;

    public MentorCommand() {
        super("mentor");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 30000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 30 seconds!");
                return;
            }
        }

        if (player.getClientConnection().getAccount() == null)
            return;
        else if (player.getClientConnection().getAccount().getMembership() < MEMBERSHIP_REQ)
            return;

        player.setMentor(!player.isMentor());

        player.clearKnownlist();
        PacketSendUtility.sendPacket(player, new SM_PLAYER_INFO(player, false));
        PacketSendUtility.broadcastPacketAndReceive(player, new SM_MOTION(player));
        player.updateKnownlist();
        PacketSendUtility.sendPacket(player, new SM_TARGET_SELECTED(player));
        player.getEffectController().updatePlayerEffectIcons();

        if (player.isMentor())
            PacketSendUtility.sendMessage(player, "You have enabled the Mentor flag!");
        else
            PacketSendUtility.sendMessage(player, "You have disabled the Mentor flag!");

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}