-- ====================================================================
-- MISSING TABLES PATCH - FIX FOR CASHSHOPMANAGER ERRORS
-- ====================================================================
-- This patch adds the missing tables that cause CashShopManager errors:
-- - shop_purchases
-- - shop_removals
--
-- Run this ONLY if you already imported the previous database
-- and are getting "Table doesn't exist" errors for these tables.
-- ====================================================================

USE `not-aion`;

-- ----------------------------
-- `shop_purchases` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT NULL,
  `added` tinyint(1) NOT NULL DEFAULT '0',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  KEY `added` (`added`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `shop_removals` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemUniqueId` int(11) NOT NULL,
  `itemOwner` int(11) NOT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `removed` int(11) NOT NULL DEFAULT '0',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `itemUniqueId` (`itemUniqueId`),
  KEY `itemOwner` (`itemOwner`),
  KEY `removed` (`removed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- PATCH COMPLETE
-- ====================================================================
-- The missing tables have been added!
-- Your CashShopManager errors should now be resolved.
-- 
-- Note: Change the database name in the USE statement above 
-- if your database is named differently than 'not-aion'
-- ====================================================================
