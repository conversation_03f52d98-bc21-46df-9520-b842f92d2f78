#
# This file is part of Aion X Emu <aionxemu.com>.
#
# This is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser Public License for more details.
#
# You should have received a copy of the GNU Lesser Public License
# along with this software.  If not, see <http://www.gnu.org/licenses/>.
#

# LoginServer will listen for connections on specified port
loginserver.network.client.port = 2106

# LoginServer will bind specified network interface
# * - bind all interfaces
loginserver.network.client.host = *

# How many times player can try to login before he get's banned for bruteforcing
loginserver.network.client.logintrybeforeban = 5

# For what time in minutes player should be banned in case of bruteforcing
loginserver.network.client.bantimeforbruteforcing = 15

# Host that will be used by LS to listen for GS connections
loginserver.network.gameserver.host = *

# Port that will be used by LS to listen for GS connections
loginserver.network.gameserver.port = 9014

# Nuber of additional threads for NIO that will handle only reading
loginserver.network.nio.threads.read = 0

# Nuber of additional threads for NIO that will handle only writing
loginserver.network.nio.threads.write = 0

# Create accounts automatically or not? -- You do not need a website / CMS if set true 
loginserver.accounts.autocreate = true
