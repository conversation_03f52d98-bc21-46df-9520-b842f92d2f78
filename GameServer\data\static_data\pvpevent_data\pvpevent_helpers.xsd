<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	version="1.0">
	<xs:include schemaLocation="../import.xsd" />
	<xs:element name="helpers" type="helpergroup" />
	<xs:complexType name="helpergroup">
		<xs:sequence>
			<xs:element ref="import" maxOccurs="unbounded" minOccurs="0"></xs:element>
			<xs:element name="helper" type="helper" minOccurs="0"
				maxOccurs="unbounded"></xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="helper">
		<xs:sequence>
			<xs:element name="npc_id" type="xs:nonNegativeInteger"
				maxOccurs="1" minOccurs="1"></xs:element>
		</xs:sequence>
		<xs:attribute name="difficulty" type="xs:int" default="0"/>
	</xs:complexType>
</xs:schema>