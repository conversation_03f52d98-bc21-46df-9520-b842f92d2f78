/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.configs.network.NetworkConfig;
import gameserver.dao.ShopDAO;
import gameserver.model.ShopCategory;
import gameserver.model.ShopItem;
import gameserver.model.ShopItemType;
import gameserver.model.gameobjects.player.Player;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;

/**
 * 
 * <AUTHOR>
 */
public class MySQL5ShopDAO extends ShopDAO {
    private static final Logger log = Logger.getLogger(MySQL5ShopDAO.class);

    @Override
    public List<ShopCategory> getCategories() {
        Connection con = null;
        List<ShopCategory> categories = new ArrayList<ShopCategory>();

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement("SELECT id, name FROM shopcategories");

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                if (rset.getString("name").equals("Sales"))
                    continue;

                ShopCategory category = new ShopCategory(rset.getInt("id"));
                category.setName(rset.getString("name"));
                category.setItems(getItems(category.getId()));

                categories.add(category);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return categories;
    }

    @Override
    public Map<Integer, ShopItem> getItems(int categoryId) {
        Connection con = null;
        Map<Integer, ShopItem> items = new HashMap<Integer, ShopItem>();

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT id, itemId, count, price, name, type FROM shopitems WHERE categoryid = ?");

            stmt.setInt(1, categoryId);
            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                ShopItem item = new ShopItem(rset.getInt("id"));
                item.setItemId(rset.getInt("itemId"));
                item.setCount(rset.getInt("count"));
                item.setPrice(rset.getInt("price"));
                item.setName(rset.getString("name"));
                item.setType(ShopItemType.valueOf(rset.getString("type")));

                items.put(item.getId(), item);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return items;
    }

    @Override
    public void logPurchase(Player player, int itemId, int count, int price) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("INSERT INTO shoplog (date, accountId, itemId, count, price) VALUES (?, ?, ?, ?, ?)");

            stmt.setTimestamp(1, new Timestamp(System.currentTimeMillis()));
            stmt.setInt(2, getAccountId(player));
            stmt.setInt(3, itemId);
            stmt.setInt(4, count);
            stmt.setInt(5, price);

            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
    }

    @Override
    public List<DonationEntry> getDonationEntries(Player player) {
        Connection con = null;
        List<DonationEntry> donations = new ArrayList<DonationEntry>();

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT date, count FROM shoplog WHERE accountId = ? AND itemId = -10 ORDER BY date DESC");
            stmt.setInt(1, getAccountId(player));

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                donations.add(new DonationEntry(rset.getTimestamp("date"), rset.getInt("count")));
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return donations;
    }

    @Override
    public float getDonationTotalMonths(Player player, int months) {
        Connection con = null;
        int total = 0;

        try {
            con = DatabaseFactory.getConnection();

            PreparedStatement stmt = con
                .prepareStatement("SELECT SUM(count) AS total FROM shoplog WHERE accountId = ? AND itemId = -10 AND date >= DATE_SUB(NOW(), INTERVAL ? MONTH)");
            stmt.setInt(1, getAccountId(player));
            stmt.setInt(2, months);

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                total += rset.getInt("total");
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return total * 0.01f;
    }

    @Override
    public float getDonationTotal(Player player) {
        Connection con = null;
        int total = 0;

        try {
            con = DatabaseFactory.getConnection();

            PreparedStatement stmt = con
                .prepareStatement("SELECT SUM(count) AS total FROM shoplog WHERE accountId = ? AND itemId = -10");
            stmt.setInt(1, getAccountId(player));

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                total += rset.getInt("total");
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return total * (NetworkConfig.GAMESERVER_ID == 24 ? 0.005f : 0.01f);
    }

    public boolean addItem(int categoryId, ShopItem shopItem) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("INSERT INTO shopitems (categoryid, itemId, count, price, name, type) VALUES (?, ?, ?, ?, ?, ?)");

            stmt.setInt(1, categoryId);
            stmt.setInt(2, shopItem.getItemId());
            stmt.setInt(3, shopItem.getCount());
            stmt.setInt(4, (int) shopItem.getPrice());
            stmt.setString(5, shopItem.getName());
            stmt.setString(6, shopItem.getType().toString());

            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        return true;
    }

    public List<ShopCategory> getCategoryList() {
        Connection con = null;
        List<ShopCategory> categories = new ArrayList<ShopCategory>();

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement("SELECT id, name FROM shopcategories");

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                ShopCategory category = new ShopCategory(rset.getInt("id"));
                category.setName(rset.getString("name"));

                categories.add(category);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return categories;
    }

    private int getAccountId(Player player) {
        Connection con = null;
        int accountId = 0;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT account_id FROM players WHERE id=?");
            stmt.setInt(1, player.getObjectId());
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                accountId = rset.getInt("account_id");
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal("MEEP!");
        }
        finally {
            DatabaseFactory.close(con);
        }
        return accountId;
    }

    @Override
    public boolean addShopPurchase(int charId, int itemId, long quantity, boolean gift,
        String gifter) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT INTO shop_purchases (char_id, item_id, quantity, gift, gifter) VALUES (?,?,?,?,?)");

        try {
            ps.setInt(1, charId);
            ps.setInt(2, itemId);
            ps.setLong(3, quantity);
            ps.setInt(4, gift ? 1 : 0);
            ps.setString(5, gifter);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error inserting shop purchase: ", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public List<ShopPurchase> getPendingPurchases() {
        List<ShopPurchase> purchases = new ArrayList<ShopPurchase>();
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT id, char_id, item_id, quantity, gift, gifter FROM shop_purchases WHERE added = 0");

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                ShopPurchase purchase = new ShopPurchase(rset.getInt("id"), rset.getInt("char_id"),
                    rset.getInt("item_id"), rset.getLong("quantity"), rset.getBoolean("gift"),
                    rset.getString("gifter"));
                purchases.add(purchase);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return purchases;
    }

    @Override
    public boolean flagPurchaseAsAdded(int id) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("UPDATE shop_purchases SET added = 1 WHERE id = ?");
            stmt.setInt(1, id);
            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }

    @Override
    public List<ShopRemoval> getPendingRemovals() {
        List<ShopRemoval> removals = new ArrayList<ShopRemoval>();
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT id, itemUniqueId, itemOwner, amount FROM shop_removals WHERE removed = 0");

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                ShopRemoval removal = new ShopRemoval(rset.getInt("id"),
                    rset.getInt("itemUniqueId"), rset.getInt("itemOwner"), rset.getInt("amount"));
                removals.add(removal);
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return removals;
    }

    @Override
    public boolean deleteRemoval(int id) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement("DELETE FROM shop_removals WHERE id = ?");
            stmt.setInt(1, id);
            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }

    @Override
    public boolean flagRemovalAsDone(int id, int amount) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("UPDATE shop_removals SET removed = ? WHERE id = ?");
            stmt.setInt(1, amount);
            stmt.setInt(2, id);
            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}