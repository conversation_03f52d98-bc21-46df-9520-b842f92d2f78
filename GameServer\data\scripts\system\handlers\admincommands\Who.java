/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.PlayerClass;
import gameserver.model.Race;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;
import gameserver.world.WorldMapType;

/**
 * Admin who command.
 * 
 * <AUTHOR>
 */

@SuppressWarnings("unused")
public class Who extends AdminCommand {

    private World world;

    /**
     * Constructor.
     */

    public Who() {
        super("who");
    }

    /**
     * {@inheritDoc}
     */

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_WHO) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length == 0 || params.length > 1) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //who <asmo | elyos | map | all> -- OR\n"
                        + "Syntax: //who <gladiator | templar | ranger | assassin | sorcerer | spiritmaster | chanter | cleric | gunner | bard | aethertech>");
            return;
        }

        PlayerClass plClass = null;

        if (params[0].toLowerCase().equals("gladiator")) {
            plClass = PlayerClass.GLADIATOR;
        }
        else if (params[0].toLowerCase().equals("templar")) {
            plClass = PlayerClass.TEMPLAR;
        }
        else if (params[0].toLowerCase().equals("ranger")) {
            plClass = PlayerClass.RANGER;
        }
        else if (params[0].toLowerCase().equals("assassin")) {
            plClass = PlayerClass.ASSASSIN;
        }
        else if (params[0].toLowerCase().equals("sorcerer")) {
            plClass = PlayerClass.SORCERER;
        }
        else if (params[0].toLowerCase().equals("spiritmaster")) {
            plClass = PlayerClass.SPIRIT_MASTER;
        }
        else if (params[0].toLowerCase().equals("chanter")) {
            plClass = PlayerClass.CHANTER;
        }
        else if (params[0].toLowerCase().equals("cleric")) {
            plClass = PlayerClass.CLERIC;
        }
        else if (params[0].toLowerCase().equals("gunner")) {
            plClass = PlayerClass.GUNNER;
        }
        else if (params[0].toLowerCase().equals("bard")) {
            plClass = PlayerClass.BARD;
        }
        else if (params[0].toLowerCase().equals("aethertech")) {
            plClass = PlayerClass.RIDER;
        }

        if (plClass != null) {
            StringBuilder sb = new StringBuilder();
            int onlineCount = 0;

            for (Player player : World.getInstance().getPlayers()) {
                if (player.getPlayerClass() == plClass) {
                    WorldMapType world = WorldMapType.getWorld(player.getWorldId());
                    onlineCount++;

                    sb.append(player.getName() + ", " + player.getCommonData().getRace().toString()
                        + ", " + (world != null ? world.toString() : player.getWorldId()) + "\n");
                }
            }

            sb.insert(0, "[" + onlineCount + " " + plClass.toString() + "'s ONLINE]\n");

            PacketSendUtility.sendMessage(admin, sb.toString());
        }
        else if (params[0].toLowerCase().equals("map")) {
            int onlineCount = 0;
            int onlineAsmo = 0;
            int onlineEly = 0;
            int onlineStaff = 0;

            for (Player player : admin.getWorldMapInstance().getPlayers()) {
                if (player.isOnline()) {
                    onlineCount++;

                    if (player.getAccessLevel() > 0)
                        onlineStaff++;

                    switch (player.getCommonData().getRace()) {
                        case ASMODIANS:
                            onlineAsmo++;
                            break;
                        case ELYOS:
                            onlineEly++;
                            break;
                    }
                }
            }

            PacketSendUtility.sendMessage(admin, "Total on map: " + onlineCount + " (staff: "
                + onlineStaff + ")");
            PacketSendUtility.sendMessage(admin, "Ely: " + onlineEly + ", Asmo: " + onlineAsmo);
        }
        else {
            String sPlayerNames = "";
            String sAsmoPlayerNames = "";
            String sElyosPlayerNames = "";
            int szAsmoCount = 0;
            int szElyosCount = 0;
            int szPlayersCount = 0;

            for (Player player : World.getInstance().getPlayers()) {
                if (player.getCommonData().getRace() == Race.ASMODIANS) {
                    sAsmoPlayerNames += player.getName() + " ; "; // or other name delimiter:)
                    szAsmoCount++;
                }
                else {
                    sElyosPlayerNames += player.getName() + " ; "; // or other name delimiter:)
                    szElyosCount++;
                }
            }

            if (params[0].toLowerCase().equals("asmo")) {
                sPlayerNames = sAsmoPlayerNames;
                szPlayersCount = szAsmoCount;
            }
            else if (params[0].toLowerCase().equals("elyos")) {
                sPlayerNames = sElyosPlayerNames;
                szPlayersCount = szElyosCount;
            }
            else if (params[0].toLowerCase().equals("all")) {
                sPlayerNames = sElyosPlayerNames + sAsmoPlayerNames;
                szPlayersCount = szElyosCount + szAsmoCount;
            }
            else {
                PacketSendUtility.sendMessage(admin, "Syntax: //who <asmo | elyos | all>");
                return;
            }
            PacketSendUtility.sendMessage(admin, "Now  " + String.valueOf(szPlayersCount)
                + " are online !\n");
            PacketSendUtility.sendMessage(admin, "Names of players : " + sPlayerNames);
        }
    }
}
