/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.dao.LadderDAO;
import gameserver.dao.LadderDAO.PlayerLadderData;
import gameserver.dao.MightDAO;
import gameserver.dao.ShopDAO;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.item.ArmorType;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE;
import gameserver.services.PremiumService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.chathandlers.UserCommand;

import java.util.HashMap;
import java.util.Map;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * 
 * <AUTHOR>
 */
public class DyeCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    private int dyeCost = 50;

    public DyeCommand() {
        super("dye");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.isTemporary())
            return;

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 5000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 5 seconds!");
                return;
            }
        }

        if (params.length < 1 || params[0] == "") {
            PacketSendUtility.sendMessage(player, "Syntax: .dye <hex code> -- costs " + dyeCost
                + " Might" + "\nSyntax: OR .dye preview <hex code> -- free but lasts 5 seconds"
                + "\nSyntax: OR .dye remove -- removes dyes returning default color");
            return;
        }
        else if (params[0].equalsIgnoreCase("preview")) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(player, "Syntax: .dye preview <hex code>");
                return;
            }

            String color = params[1];
            if (color.startsWith("#"))
                color = color.substring(1);

            int rgb = 0;
            int bgra = 0;
            try {
                rgb = Integer.parseInt(color, 16);
                bgra = 0xFF | ((rgb & 0xFF) << 24) | ((rgb & 0xFF00) << 8)
                    | ((rgb & 0xFF0000) >>> 8);
            }
            catch (NumberFormatException e) {
                PacketSendUtility.sendMessage(player, color + " is not a valid color parameter!");
                return;
            }

            previewDyeMakeover(player, bgra);
        }
        else if (params[0].equalsIgnoreCase("remove")) {
            purchaseDyeMakeover(player, 0, "remove");
        }
        else {
            String color = params[0];
            if (color.startsWith("#"))
                color = color.substring(1);

            int rgb = 0;
            int bgra = 0;
            try {
                rgb = Integer.parseInt(color, 16);
                bgra = 0xFF | ((rgb & 0xFF) << 24) | ((rgb & 0xFF00) << 8)
                    | ((rgb & 0xFF0000) >>> 8);
            }
            catch (NumberFormatException e) {
                PacketSendUtility.sendMessage(player, color + " is not a valid color parameter!");
                return;
            }

            purchaseDyeMakeover(player, bgra, color);
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void purchaseDyeMakeover(Player player, int bgra, String color) {
        int might = DAOManager.getDAO(MightDAO.class).getMight(player);
        if (!PremiumService.isSuperPremium(player) && might < dyeCost) {
            PacketSendUtility.sendMessage(player,
                "Error! You do not have enough Might to purchase the Dye-makeover!");
            return;
        }

        for (Item targetItem : player.getEquipment().getEquippedItemsWithoutStigma()) {
            ArmorType armorType = targetItem.getItemTemplate().getArmorType();
            if (armorType != null && (armorType == ArmorType.ARROW || armorType == ArmorType.SHARD))
                continue;

            targetItem.setItemColor(bgra);
            PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(targetItem));
        }

        PacketSendUtility.broadcastPacket(player,
            new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), player.getEquipment()
                .getEquippedItemsWithoutStigma()), true);
        player.getEquipment().setPersistentState(PersistentState.UPDATE_REQUIRED);

        PlayerLadderData data = getLadderDAO().getPlayerLadderData(player);

        int rank = data.getRank();

        if ((rank >= 1 && rank <= 10) || PremiumService.isSuperPremium(player)) {
            if (color.equalsIgnoreCase("remove"))
                PacketSendUtility.sendMessage(player, "You have purchased a free Dye-removal!");
            else
                PacketSendUtility.sendMessage(player,
                    "You have purchased a free Dye-makeover with color #" + color + "!");
        }
        else {
            DAOManager.getDAO(MightDAO.class).addMight(player, -dyeCost);

            if (color.equalsIgnoreCase("remove"))
                PacketSendUtility.sendMessage(player, "You have purchased a Dye-removal!");
            else
                PacketSendUtility.sendMessage(player,
                    "You have purchased a Dye-makeover with color #" + color + "!");

            PacketSendUtility.sendMessage(player, "You have " + (might - dyeCost) + " Might left.");

            DAOManager.getDAO(ShopDAO.class).logPurchase(player, -2, bgra, dyeCost);
        }
    }

    private void previewDyeMakeover(final Player player, int bgra) {
        final Map<Item, Integer> oldColors = new HashMap<Item, Integer>();
        for (Item targetItem : player.getEquipment().getEquippedItemsWithoutStigma()) {
            ArmorType armorType = targetItem.getItemTemplate().getArmorType();
            if (armorType != null && (armorType == ArmorType.ARROW || armorType == ArmorType.SHARD))
                continue;

            oldColors.put(targetItem, new Integer(targetItem.getItemColor()));

            targetItem.setItemColor(bgra);
        }

        PacketSendUtility.broadcastPacket(player,
            new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), player.getEquipment()
                .getEquippedItemsWithoutStigma()), true);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (Map.Entry<Item, Integer> entry : oldColors.entrySet())
                    entry.getKey().setItemColor(entry.getValue());
            }
        }, 50);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                PacketSendUtility.broadcastPacket(player,
                    new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), player.getEquipment()
                        .getEquippedItemsWithoutStigma()), true);
            }
        }, 5000);
    }

    private LadderDAO getLadderDAO() {
        return DAOManager.getDAO(LadderDAO.class);
    }
}