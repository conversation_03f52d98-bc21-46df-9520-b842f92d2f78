/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.controllers.attack.AttackStatus;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Kisk;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;

/**
 * <AUTHOR>
 */
public class Kill extends AdminCommand {
    public Kill() {
        super("kill");
    }

    @Override
    public void executeCommand(final Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_KILL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length > 0) {
            if ("kisk".startsWith(params[0])) {
                admin.getWorldMapInstance().doOnAllNpcs(new Executor<Npc>() {
                    @Override
                    public boolean run(Npc npc) {
                        if (npc instanceof Kisk)
                            npc.getController().onDie(npc);

                        return true;
                    }
                });

                PacketSendUtility.sendMessage(admin, "All kisks on map have been killed.");
                return;
            }
            else if ("radius".startsWith(params[0])) {
                final int radius;

                try {
                    radius = Integer.parseInt(params[1]);

                    if (radius <= 0)
                        throw new Exception();
                }
                catch (Exception e) {
                    PacketSendUtility
                        .sendMessage(admin,
                            "Error! Please specify a positive number as radius: //kill radius <distance>");
                    return;
                }

                admin.getWorldMapInstance().doOnAllObjects(new Executor<AionObject>() {
                    @Override
                    public boolean run(AionObject ao) {
                        if (!(ao instanceof Creature))
                            return true;

                        Creature creature = (Creature) ao;

                        if (creature instanceof Player && ((Player) creature).getAccessLevel() > 0)
                            return true;
                        else if (!MathUtil.isIn3dRange(admin, creature, radius))
                            return true;

                        creature.getController().onAttack(admin, 1, AttackStatus.NORMALHIT, true);
                        creature.getController().onAttack(admin,
                            creature.getLifeStats().getMaxHp() + 1, AttackStatus.NORMALHIT, true);

                        return true;
                    }
                });
            }
        }

        VisibleObject target = admin.getTarget();

        if (target == null) {
            PacketSendUtility.sendMessage(admin, "No target selected");
            return;
        }

        if (target instanceof Creature) {
            Creature creature = (Creature) target;

            if (creature instanceof Player && ((Player) creature).isInvul())
                ((Player) creature).setInvul(false);

            creature.getController().onAttack(admin, 1, AttackStatus.NORMALHIT, true);
            creature.getController().onAttack(admin, creature.getLifeStats().getMaxHp() + 1,
                AttackStatus.NORMALHIT, true);
        }
    }
}
