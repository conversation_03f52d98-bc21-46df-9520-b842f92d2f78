<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    
    <xs:element name="pet_skill_templates">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="pet_skill" type="PetSkill" minOccurs="1" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="PetSkill">
        <xs:attribute name="order_skill" type="xs:int"/>
        <xs:attribute name="pet_id" type="xs:int"/>
        <xs:attribute name="skill_id" type="xs:int"/>
    </xs:complexType>
</xs:schema>