/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package mysql5;

import gameserver.dao.LadderDAO;
import gameserver.model.gameobjects.player.Player;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;
import com.mysql.jdbc.exceptions.jdbc4.MySQLTransactionRollbackException;

/**
 * 
 * <AUTHOR>
 */
public class MySQL5LadderDAO extends LadderDAO {
    private static final Logger log = Logger.getLogger(MySQL5LadderDAO.class);

    public void addWin(Player player) {
        addPlayerLadderData(player, "wins", 1);
    }

    public void addLoss(Player player) {
        addPlayerLadderData(player, "losses", 1);
    }

    public void addLeave(Player player) {
        addPlayerLadderData(player, "leaves", 1);
    }

    public void addRating(Player player, int rating) {
        addPlayerLadderData(player, "rating", rating);
    }

    public void setWins(Player player, int wins) {
        setPlayerLadderData(player, "wins", wins);
    }

    public void setLosses(Player player, int losses) {
        setPlayerLadderData(player, "losses", losses);
    }

    public void setLeaves(Player player, int leaves) {
        setPlayerLadderData(player, "leaves", leaves);
    }

    public void setRating(Player player, int rating) {
        setPlayerLadderData(player, "rating", rating);
    }

    public int getWins(Player player) {
        return getPlayerLadderData(player, "wins");
    }

    public int getLosses(Player player) {
        return getPlayerLadderData(player, "losses");
    }

    public int getLeaves(Player player) {
        return getPlayerLadderData(player, "leaves");
    }

    public int getRating(Player player) {
        return getPlayerLadderData(player, "rating");
    }

    public void updateRanks() {
        Connection con = null;

        // List<PlayerInfo> players = new ArrayList<PlayerInfo>();

        try {
            con = DatabaseFactory.getConnection();
            /*
             * PreparedStatement stmt = con .prepareStatement(
             * "SELECT player_id, last_update, rating, wins, rank FROM ladder_player WHERE wins > 0 OR losses > 0 OR leaves > 0 OR rating != 1000 OR rank != -1 OR last_rank != -1 ORDER BY rating DESC, wins DESC"
             * ); ResultSet rset = stmt.executeQuery(); while (rset.next()) { PlayerInfo plInfo = new
             * PlayerInfo(rset.getInt("player_id"), rset.getInt("rating"), rset.getTimestamp("last_update"),
             * rset.getInt("wins"), rset.getInt("rank")); players.add(plInfo); } rset.close(); stmt.close();
             */

            PreparedStatement stmt = con
                .prepareStatement("UPDATE ladder_player JOIN "
                    + "(SELECT p.player_id,@rank:=@rank+1 AS rank "
                    + "FROM "
                    + "ladder_player p "
                    + "JOIN "
                    + "(SELECT @rank:=0) r "
                    + "WHERE "
                    + "(wins>0 OR losses>0 OR leaves>0 OR rating!=1000 OR rank!=-1 OR last_rank!=-1) "
                    + "ORDER BY p.rating DESC,wins DESC) t "
                    + "ON "
                    + "(t.player_id = ladder_player.player_id) "
                    + "SET "
                    + "ladder_player.last_rank = IF( "
                    + "TIMESTAMPDIFF(HOUR, IFNULL(ladder_player.last_update, FROM_UNIXTIME(0)), NOW()) >= 24, "
                    + "ladder_player.rank, "
                    + "ladder_player.last_rank), "
                    + "ladder_player.last_update = IF( "
                    + "TIMESTAMPDIFF(HOUR, IFNULL(ladder_player.last_update, FROM_UNIXTIME(0)), NOW()) >= 24, "
                    + "NOW(), " + "ladder_player.last_update), " + "ladder_player.rank = t.rank;");
            stmt.execute();
            stmt.close();
        }
        catch (SQLException e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        /*
         * Collections.sort(players, new Comparator<PlayerInfo>() {
         * @Override public int compare(PlayerInfo o1, PlayerInfo o2) { int result =
         * Integer.valueOf(o1.getRating()).compareTo( Integer.valueOf(o2.getRating())); if (result != 0) return -result;
         * result = Integer.valueOf(o1.getWins()).compareTo(Integer.valueOf(o2.getWins())); if (result != 0) return
         * -result; result = Integer.valueOf(o1.getPlayerId()).compareTo( Integer.valueOf(o2.getPlayerId())); return
         * result; } }); if (players.size() > 0) { int i = 1; try { con = DatabaseFactory.getConnection();
         * PreparedStatement stmtRank = con .prepareStatement("UPDATE ladder_player SET rank = ? WHERE player_id = ?");
         * PreparedStatement stmtLast = con
         * .prepareStatement("UPDATE ladder_player SET last_rank = ?, last_update = ? WHERE player_id = ?"); String
         * updateRank = "UPDATE ladder_player SET rank = CASE player_id"; for (PlayerInfo plInfo : players) { int
         * playerId = plInfo.getPlayerId(); Timestamp update = plInfo.getLastUpdate(); if (update == null ||
         * update.equals(new Timestamp(0)) || (update.getTime() + 24 * 60 * 60 * 1000) < System.currentTimeMillis()) {
         * stmtLast.setInt(1, plInfo.getRank()); stmtLast.setTimestamp(2, new Timestamp(System.currentTimeMillis()));
         * stmtLast.setInt(3, playerId); stmtLast.addBatch(); } // updateRank += " WHEN " + playerId + " THEN " + i;
         * stmtRank.setInt(1, i); stmtRank.setInt(2, playerId); stmtRank.addBatch(); i++; } updateRank +=
         * " ELSE -1 END"; //updateRank += " END WHERE player_id IN ("; //i = 0; //for (PlayerInfo plInfo : players) {
         * // updateRank += (i++ > 0 ? "," : "") + plInfo.getPlayerId(); //} //updateRank += ")"; //PreparedStatement
         * stmt = con.prepareStatement(updateRank); //stmt.executeUpdate(); stmtRank.executeBatch();
         * stmtLast.executeBatch(); // stmtRank.close(); stmtLast.close(); } catch (SQLException e) { log.error(e); }
         * finally { DatabaseFactory.close(con); } }
         */
    }

    public int getRank(Player player) {
        return getPlayerLadderData(player, "rank");
    }

    public void addPlayerLadderData(Player player, String data, int value) {
        Connection con = null;

        boolean success = false;
        while (!success) {
            try {
                con = DatabaseFactory.getConnection();

                PreparedStatement stmt = con
                    .prepareStatement("INSERT INTO ladder_player (player_id, " + data
                        + ") VALUES (?,?) ON DUPLICATE KEY UPDATE " + data + " = " + data + " + ?");
                stmt.setInt(1, player.getObjectId());
                stmt.setInt(2, "rating".equals(data) ? 1000 + value : value);
                stmt.setInt(3, value);
                stmt.execute();
                success = true;
            }
            catch (MySQLTransactionRollbackException e) {
                log.error("Deadlock on updating ladder data for player " + player.getName()
                    + ". Re-trying...");
            }
            catch (SQLException e) {
                log.error(e);
            }
            finally {
                DatabaseFactory.close(con);
            }
        }

        /*
         * if (this.checkExists(player)) { try { con = DatabaseFactory.getConnection(); PreparedStatement stmt =
         * con.prepareStatement("UPDATE ladder_player SET " + data + " = " + data + " + ? WHERE player_id = ?");
         * stmt.setInt(1, value); stmt.setInt(2, player.getObjectId()); stmt.execute(); } catch (SQLException e) {
         * log.error(e); } finally { DatabaseFactory.close(con); } } else { try { con = DatabaseFactory.getConnection();
         * PreparedStatement stmt = con .prepareStatement("INSERT INTO ladder_player (player_id, " + data +
         * ") VALUES (?, ?)"); stmt.setInt(1, player.getObjectId()); stmt.setInt(2, "rating".equals(data) ? 1000 + value
         * : value); stmt.execute(); } catch (SQLException e) { log.error(e); } finally { DatabaseFactory.close(con); }
         * }
         */
    }

    public void setPlayerLadderData(Player player, String data, int value) {
        Connection con = null;

        boolean success = false;
        while (!success) {
            try {
                con = DatabaseFactory.getConnection();
                PreparedStatement stmt = con
                    .prepareStatement("INSERT INTO ladder_player (player_id, " + data
                        + ") VALUES (?,?) ON DUPLICATE KEY UPDATE " + data + " = ?");
                stmt.setInt(1, player.getObjectId());
                stmt.setInt(2, value);
                stmt.setInt(3, value);
                stmt.execute();
                success = true;
            }
            catch (MySQLTransactionRollbackException e) {
                log.error("Deadlock on updating ladder data for player " + player.getName()
                    + ". Re-trying...");
            }
            catch (SQLException e) {
                log.error(e);
            }
            finally {
                DatabaseFactory.close(con);
            }
        }

        /*
         * if (this.checkExists(player)) { try { con = DatabaseFactory.getConnection(); PreparedStatement stmt =
         * con.prepareStatement("UPDATE ladder_player SET " + data + " = ? WHERE player_id = ?"); stmt.setInt(1, value);
         * stmt.setInt(2, player.getObjectId()); stmt.execute(); } catch (SQLException e) { log.error(e); } finally {
         * DatabaseFactory.close(con); } } else { try { con = DatabaseFactory.getConnection(); PreparedStatement stmt =
         * con .prepareStatement("INSERT INTO ladder_player (player_id, " + data + ") VALUES (?, ?)"); stmt.setInt(1,
         * player.getObjectId()); stmt.setInt(2, value); stmt.execute(); } catch (SQLException e) { log.error(e); }
         * finally { DatabaseFactory.close(con); } }
         */
    }

    public int getPlayerLadderData(Player player, String data) {
        Connection con = null;
        int value = 0;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement("SELECT " + data
                + " FROM ladder_player WHERE player_id = ?");

            stmt.setInt(1, player.getObjectId());
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                value = rset.getInt(data);
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        if (data.equals("rating") && value == 0)
            return 1000;

        return value;
    }

    public int getPlayerLadderData(Integer playerId, String data) {
        Connection con = null;
        int value = 0;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement("SELECT " + data
                + " FROM ladder_player WHERE player_id = ?");

            stmt.setInt(1, playerId);
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                value = rset.getInt(data);
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        if (data.equals("rating") && value == 0)
            return 1000;

        return value;
    }

    public Timestamp getPlayerLadderUpdate(Player player) {
        Connection con = null;
        Timestamp value = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT last_update FROM ladder_player WHERE player_id = ?");

            stmt.setInt(1, player.getObjectId());
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                value = rset.getTimestamp("last_update");
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return value;
    }

    public void setPlayerLadderUpdate(Player player, Timestamp value) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("UPDATE ladder_player SET last_update = ? WHERE player_id = ?");
            stmt.setTimestamp(1, value);
            stmt.setInt(2, player.getObjectId());
            stmt.execute();
        }
        catch (SQLException e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
    }

    public void setPlayerLadderUpdate(Integer playerId, Timestamp value) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("UPDATE ladder_player SET last_update = ? WHERE player_id = ?");
            stmt.setTimestamp(1, value);
            stmt.setInt(2, playerId);
            stmt.execute();
        }
        catch (SQLException e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
    }

    public PlayerLadderData getPlayerLadderData(Player player) {
        Connection con = null;
        PlayerLadderData data = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT * FROM ladder_player WHERE player_id = ?");

            stmt.setInt(1, player.getObjectId());
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                data = new PlayerLadderData(player, rset.getInt("rating"), rset.getInt("rank"),
                    rset.getInt("wins"), rset.getInt("losses"), rset.getInt("leaves"),
                    rset.getTimestamp("last_update"));
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        if (data == null)
            data = new PlayerLadderData(player, 1000, 0, 0, 0, 0, new Timestamp(0));

        return data;
    }

    public List<String> getPlayerTopList(int top, int bottom) {
        Connection con = null;
        List<String> list = new ArrayList<String>();

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT name FROM ladder_player,players WHERE player_id = id AND rank > 0 ORDER BY rank ASC LIMIT ?,?");

            stmt.setInt(1, top - 1);
            stmt.setInt(2, bottom - top + 1);

            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                list.add(rset.getString("name"));
            }

            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return list;
    }
    
    public void addRating(int playerObjId, int rating) {
        PreparedStatement ps = DB.prepareStatement("UPDATE ladder_player SET rating = rating + ? WHERE player_id = ?");
        
        try {
            ps.setInt(1, rating);
            ps.setInt(2, playerObjId);
            
            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error adding rating to playerObjId " + playerObjId, e);
        }
        finally {
            DB.close(ps);
        }
    }
    
    public int getRating(int playerObjId) {
        PreparedStatement ps = DB.prepareStatement("SELECT rating FROM ladder_player WHERE player_id = ?");
        
        try {
            ps.setInt(1, playerObjId);
            
            ResultSet rs = ps.executeQuery();
            
            if (rs.next())
                return rs.getInt("rating");
        }
        catch (Exception e) {
            log.error("Error fetching rating of playerObjId " + playerObjId, e);
        }
        finally {
            DB.close(ps);
        }
        
        return 1000;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }

    private class PlayerInfo {
        private int playerId;
        private int rating;
        private Timestamp lastUpdate;
        private int wins;
        private int rank;

        public PlayerInfo(int playerId, int rating, Timestamp lastUpdate, int wins, int rank) {
            this.playerId = playerId;
            this.rating = rating;
            this.lastUpdate = lastUpdate;
            this.wins = wins;
            this.rank = rank;
        }

        public int getPlayerId() {
            return playerId;
        }

        public int getRating() {
            return rating;
        }

        public Timestamp getLastUpdate() {
            return lastUpdate;
        }

        public int getWins() {
            return wins;
        }

        public int getRank() {
            return rank;
        }
    }
}
