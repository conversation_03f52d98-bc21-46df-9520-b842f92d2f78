/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.controllers.MoveController.MoveDirection;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.CreatureGameStats;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.List;

public class Stat extends AdminCommand {
    public Stat() {
        super("status");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_STAT) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (admin.getTarget() == null) {
            PacketSendUtility.sendMessage(admin, "You have to select a target");
            return;
        }

        VisibleObject target = admin.getTarget();

        if (!(target instanceof Creature)) {
            PacketSendUtility.sendMessage(admin, "Your target is not a Creature");
            return;
        }

        Creature cTarget = (Creature) target;

        if (params.length >= 1) {
            if ("debug".equalsIgnoreCase(params[0]) && cTarget instanceof Player) {
                Player pTarget = (Player) cTarget;
                PacketSendUtility.sendMessage(admin, "Weapon Mastery Effect: "
                    + pTarget.getEffectController().getWeaponMastery());
                PacketSendUtility.sendMessage(admin, "Sub Mastery Effect: "
                    + pTarget.getEffectController().getSubWeaponMastery());
                PacketSendUtility.sendMessage(admin, "Dual Mastery: "
                    + pTarget.getSkillList().getDualMasterySkill() + ", Effect: "
                    + pTarget.getEffectController().getDualMastery() + " ("
                    + pTarget.getEffectController().getDualEffect() + ")");
                return;
            }

            StatEnum stat = null;

            try {
                stat = StatEnum.valueOf(params[0].toUpperCase());
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(admin, "Invalid stat " + params[0] + " specified!");
                return;
            }

            CreatureGameStats<?> cgs = cTarget.getGameStats();

            PacketSendUtility.sendMessage(admin, ">>> Stats information about "
                + cTarget.getClass().getSimpleName() + " \"" + cTarget.getName() + "\"");
            PacketSendUtility.sendMessage(admin,
                stat + ": " + cgs.getBaseStat(stat) + " (" + cgs.getStatBonus(stat) + ")");

            return;
        }

        PacketSendUtility.sendMessage(admin, ">>> Stats information about "
            + cTarget.getClass().getSimpleName() + " \"" + cTarget.getName() + "\"");
        if (cTarget.getGameStats() != null) {
            CreatureGameStats<?> cgs = cTarget.getGameStats();
            for (int i = 0; i < StatEnum.values().length; i++) {
                if (cgs.getCurrentStat(StatEnum.values()[i]) != 0) {
                    PacketSendUtility.sendMessage(admin,
                        StatEnum.values()[i] + ": " + cgs.getBaseStat(StatEnum.values()[i]) + " ("
                            + cgs.getStatBonus(StatEnum.values()[i]) + ")");
                }
            }
        }

        if (cTarget.getController().getAuraRangeRate() != 1.00f)
            PacketSendUtility.sendMessage(admin, String.format("EXTEND_AURA_RANGE_RATE: %.02f",
                cTarget.getController().getAuraRangeRate()));

        if (cTarget.getObserveController().getBaseMagicalDamageMultiplier(true) != 1.00f)
            PacketSendUtility.sendMessage(admin, String.format("BOOST_SPELL_ATTACK: %.02f", cTarget
                .getObserveController().getBaseMagicalDamageMultiplier(true)));

        if (cTarget.getController().getHealRate() != 1.00f)
            PacketSendUtility.sendMessage(admin,
                String.format("HEAL_RATE: %.02f", cTarget.getController().getHealRate()));

        if (cTarget.getController().getHealDeboostRate() != 1.00f)
            PacketSendUtility.sendMessage(admin, String.format("HEAL_RATE_BOOST: %.02f", cTarget
                .getController().getHealDeboostRate()));

        List<MoveDirection> directions = MoveDirection.getMoveDirections(admin.getMoveController()
            .getMoveDirection());

        String dir = "MOVE_DIRECTION: ";
        for (MoveDirection direction : directions)
            dir += direction.name() + ", ";

        PacketSendUtility.sendMessage(admin, dir);

        PacketSendUtility.sendMessage(admin, ">>> End of stats information");
    }
}
