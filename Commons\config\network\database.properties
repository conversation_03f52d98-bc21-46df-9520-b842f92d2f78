#
# This file is part of Aion X Emu <aionxemu>.
#
# This is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser Public License for more details.
#
# You should have received a copy of the GNU Lesser Public License
# along with this software.  If not, see <http://www.gnu.org/licenses/>.
#
# You should have received a copy of the GNU General Public License
# along with this software.  If not, see <http://www.gnu.org/licenses/>.
#
# ----------------------------
# Database Config
# ----------------------------

# This class represents database driver class that will be used while connecting to database
database.driver = com.mysql.jdbc.Driver

# This is database url. 
database.url = ****************************************************************************

# Database user
database.user = root

# Database password
# NOTE: Change this! Root password is not safe!
database.password = password

# Minimum amount of database connections that will allways in the pool
database.connections.min = 5

# Maximum amount of DB connections that server can use
database.connections.max = 10

# Script context that will be loaded by database factory, it should implement DAO instances
database.scriptcontext.descriptor = ./data/scripts/system/database.xml