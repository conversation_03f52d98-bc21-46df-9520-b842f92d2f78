<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- edited with XMLSpy v2010 rel. 3 (http://www.altova.com) by MESMERiZE (MSM) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
    <xs:element name="pets">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="pet" type="pet" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="pet">
        <xs:sequence>
            <xs:element name="petfunction" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="type" type="xs:string"/>
                    <xs:attribute name="id" type="xs:int"/>
                    <xs:attribute name="slots" type="xs:int"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="petstats" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="reaction"/>
                    <xs:attribute name="run_speed" type="xs:float"/>
                    <xs:attribute name="walk_speed" type="xs:float"/>
                    <xs:attribute name="height" type="xs:float"/>
                    <xs:attribute name="altitude" type="xs:float"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:int"/>
        <xs:attribute name="name" type="xs:string"/>
        <xs:attribute name="nameid" type="xs:int"/>
    </xs:complexType>
</xs:schema>