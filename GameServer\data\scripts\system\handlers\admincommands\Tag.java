/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Tag extends AdminCommand {

    public Tag() {
        super("tag");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < 1) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        admin.setShowTag(!admin.showTag());

        admin.clearKnownlist();
        PacketSendUtility.sendPacket(admin, new SM_PLAYER_INFO(admin, false));
        PacketSendUtility.broadcastPacketAndReceive(admin, new SM_MOTION(admin));
        admin.updateKnownlist();
        admin.getEffectController().updatePlayerEffectIcons();

        if (admin.showTag())
            PacketSendUtility.sendMessage(admin, "You have re-enabled your tag.");
        else
            PacketSendUtility.sendMessage(admin, "Your tag is no longer visible.");
    }
}
