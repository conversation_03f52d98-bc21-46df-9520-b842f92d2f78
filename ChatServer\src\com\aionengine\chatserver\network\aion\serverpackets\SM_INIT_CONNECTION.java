package com.aionengine.chatserver.network.aion.serverpackets;

import org.jboss.netty.buffer.ChannelBuffer;

import com.aionengine.chatserver.network.aion.AbstractServerPacket;
import com.aionengine.chatserver.network.netty.handler.ClientChannelHandler;

/**
 * 
 * <AUTHOR>
 *
 */
public class SM_INIT_CONNECTION extends AbstractServerPacket
{
	private int unk;
	
	public SM_INIT_CONNECTION(int unk)
	{
		super(0x31);
		this.unk = unk;
	}

	@Override
	protected void writeImpl(Client<PERSON><PERSON><PERSON><PERSON>and<PERSON> cHandler, ChannelBuffer buf)
	{
		writeC(buf, getOpCode());
		writeC(buf, 0x40);
		writeD(buf, unk);
		writeH(buf, 0);
	}

}
