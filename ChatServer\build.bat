@echo off
title Building ChatServer - Aion X Emu
echo ========================================
echo Building ChatServer...
echo ========================================

REM Set Java 1.6 environment
set JAVA_HOME=C:\Program Files\Java\jdk1.6.0_45
set PATH=%JAVA_HOME%\bin;%PATH%

echo Using Java 1.6 from: %JAVA_HOME%
echo.

REM Change to ChatServer directory and build
cd /d "%~dp0"
..\Tools\Ant\bin\ant.bat clean dist

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ChatServer build completed successfully!
    echo ========================================
) else (
    echo.
    echo ========================================
    echo ChatServer build FAILED!
    echo ========================================
)

pause
