	<!--
		This file is part of Aion X Emu <aionxemu>.

		This is free software: you can redistribute it and/or modify
		it under the terms of the GNU Lesser Public License as published by
		the Free Software Foundation, either version 3 of the License, or
		(at your option) any later version.

		This software is distributed in the hope that it will be useful,
		but WITHOUT ANY WARRANTY; without even the implied warranty of
		ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
		GNU Lesser Public License for more details.

		You should have received a copy of the GNU Lesser Public License
		along with this software.  If not, see <http://www.gnu.org/licenses/>.
	-->

<!--
	File that is responsible for network address mapping.
	We can have any number of ip ranges mapped to various network interfaces
	Useful for those who run server in local areas with two (or more) networks

	For ipconfig's "default" attribute both DNS and ip address is supported.
	Example: <ipconfig default="**************">
	Examples: <ipconfig default="google.de">
	Change:   <ipconfig default="Your IP Here">   to make public
	-->

<ipconfig default="127.0.0.1"> 
	<!--
		IANA-reserved private IPv4 network ranges
		Access only from local networks, external client can't have any of
		the following ip addresses

	<iprange min="10.0.0.0" max="**************" address="10.0.0.0"/>
	<iprange min="**********" max="**************" address="**********"/>
	<iprange min="***********" max="***************" address="***********"/>
	-->
</ipconfig>
