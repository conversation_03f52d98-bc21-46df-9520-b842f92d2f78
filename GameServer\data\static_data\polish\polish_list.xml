<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<polishes>
	<polish id="2" name="test_polish_set_01">
		<group id="1" prob="500">
			<modifiers>
				<rate value="-100" bonus="true" name="BOOST_HATE"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<rate value="100" bonus="true" name="BOOST_HATE"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<rate value="-50" bonus="true" name="BOOST_HATE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="3" name="test_polish_set_02">
		<group id="1" prob="1000">
			<modifiers>
				<add value="347" bonus="true" name="MAXHP"/>
				<add value="100" bonus="true" name="MAXMP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="4" name="test_polish_set_03">
		<group id="1" prob="100">
			<modifiers>
				<add value="100" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="101" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="102" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="103" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="104" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="105" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="100">
			<modifiers>
				<add value="106" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="100">
			<modifiers>
				<add value="107" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="108" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="10" prob="100">
			<modifiers>
				<add value="109" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="5" name="test_polish_set_04">
		<group id="1" prob="1000">
			<modifiers>
				<add value="100" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAXMP"/>
				<rate value="-1" bonus="true" name="ATTACK_SPEED"/>
				<rate value="2" bonus="true" name="SPEED"/>
				<rate value="2" bonus="true" name="FLY_SPEED"/>
				<rate value="1" bonus="true" name="BOOST_CASTING_TIME"/>
				<add value="50" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="50" bonus="true" name="PVP_DEFEND_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="6" name="test_polish_set_05">
		<group id="1" prob="200">
			<modifiers>
				<add value="1" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="2" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="3" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="4" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="7" name="test_polish_set_06">
		<group id="1" prob="600">
			<modifiers>
				<add value="600" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="400" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="8" name="phydef_r_q_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="60" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="160" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="50">
			<modifiers>
				<add value="20" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="50">
			<modifiers>
				<add value="30" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="50">
			<modifiers>
				<add value="22" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="250">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="140" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="40" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="9" name="magdef_r_q_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="40" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="160" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="50">
			<modifiers>
				<add value="20" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="50">
			<modifiers>
				<add value="30" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="50">
			<modifiers>
				<add value="22" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="250">
			<modifiers>
				<add value="35" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="140" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="10" name="phyatt_l_q_polish_set_01">
		<group id="1" prob="250">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="250">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="250">
			<modifiers>
				<add value="7" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="4" prob="250">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="11" name="magatt_l_q_polish_set_01">
		<group id="1" prob="250">
			<modifiers>
				<add value="58" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="250">
			<modifiers>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="250">
			<modifiers>
				<add value="54" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="4" prob="250">
			<modifiers>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="12" name="phyatt_r_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="22" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="32" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="3" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="29" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="13" name="magatt_r_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="42" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="11" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="10" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="5" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="26" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="6" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="14" name="phydef_r_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="80" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="20" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="22" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="140" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="40" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="15" name="magdef_r_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="20" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="22" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="35" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="140" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="16" name="phyatt_l_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="35" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="4" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="32" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="17" name="magatt_l_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="14" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="34" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="7" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="18" name="phydef_l_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="23" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="33" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="25" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="60" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="160" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="19" name="magdef_l_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="23" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="33" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="25" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="40" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="200">
			<modifiers>
				<add value="160" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="200">
			<modifiers>
				<add value="35" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="20" name="phyatt_l_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="41" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="21" name="magatt_l_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="42" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="11" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="38" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="22" name="phydef_l_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="29" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="39" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="31" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="80" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="70" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="23" name="magdef_l_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="29" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="39" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="31" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="45" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="24" name="assist_l_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="20" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="220" bonus="true" name="MAXMP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="14" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="25" name="resist_l_p_polish_set_01">
		<group id="1" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="111">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="111">
			<modifiers>
				<add value="22" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="111">
			<modifiers>
				<add value="11" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="111">
			<modifiers>
				<add value="22" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="111">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="111">
			<modifiers>
				<add value="9" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="111">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="26" name="phyatt_u_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="7" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="42" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="3" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="27" name="magatt_u_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="54" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="42" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="3" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="28" name="phydef_u_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXHP"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="29" name="magdef_u_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXHP"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="30" name="assist_u_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_HEAL"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXMP"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="20" bonus="true" name="BOOST_HEAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="BOOST_HEAL"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="31" name="resist_u_p_polish_set_01">
		<group id="1" prob="111">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="111">
			<modifiers>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="111">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_RESIST"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="111">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_RESIST"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="111">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="111">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="111">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="32" name="hybatt_u_p_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="33" name="hybdef_u_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="42" bonus="true" name="EVASION"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="BLOCK"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="32" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="34" name="phyatt_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="53" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="53" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="50" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="35" name="magatt_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="62" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="38" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="38" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="58" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="34" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="36" name="phydef_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="300" bonus="true" name="MAXHP"/>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="41" bonus="true" name="PARRY"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="51" bonus="true" name="EVASION"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="43" bonus="true" name="BLOCK"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="150">
			<modifiers>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="37" name="magdef_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="300" bonus="true" name="MAXHP"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="100">
			<modifiers>
				<add value="41" bonus="true" name="PARRY"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="51" bonus="true" name="EVASION"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="43" bonus="true" name="BLOCK"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="150">
			<modifiers>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="38" name="assist_u_polish_set_01">
		<group id="1" prob="250">
			<modifiers>
				<add value="26" bonus="true" name="BOOST_HEAL"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="250">
			<modifiers>
				<add value="42" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="250">
			<modifiers>
				<add value="56" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="280" bonus="true" name="MAXHP"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="50">
			<modifiers>
				<add value="300" bonus="true" name="MAXMP"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="24" bonus="true" name="BOOST_HEAL"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="39" name="resist_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="42" bonus="true" name="MAGICAL_RESIST"/>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="38" bonus="true" name="MAGICAL_RESIST"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="40" name="hybatt_u_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="58" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="53" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="38" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="54" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="41" name="hybdef_u_polish_set_01">
		<group id="1" prob="275">
			<modifiers>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="51" bonus="true" name="EVASION"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="43" bonus="true" name="BLOCK"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="41" bonus="true" name="PARRY"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="275">
			<modifiers>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="42" name="phyatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="9" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="56" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="43" name="magatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="66" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="42" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="200">
			<modifiers>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="44" name="phydef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="350">
			<modifiers>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="350">
			<modifiers>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="45" name="magdef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="350">
			<modifiers>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="350">
			<modifiers>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="46" name="assist_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="300">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="320" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="47" name="resist_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="300">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="48" name="hybatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="66" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="66" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="9" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="150">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="49" name="hybdef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="320" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="50" name="cash_phyatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="51" name="cash_magatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="52" name="cash_phydef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="53" name="cash_magdef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="54" name="cash_assist_e_polish_set_01">
		<group id="1" prob="250">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="340" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="250">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="72" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="250">
			<modifiers>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="100">
			<modifiers>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="67" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="50">
			<modifiers>
				<add value="360" bonus="true" name="MAXMP"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="67" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="55" name="cash_resist_e_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="40" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="40" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="20" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="37" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="100">
			<modifiers>
				<add value="40" bonus="true" name="MAGICAL_RESIST"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="56" name="cash_hybatt_e_polish_set_01">
		<group id="1" prob="200">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="48" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="40" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="24" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="40" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="37" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="22" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="45" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="150">
			<modifiers>
				<add value="37" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="22" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="57" name="cash_hybdef_e_polish_set_01">
		<group id="1" prob="275">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="72" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="60" bonus="true" name="EVASION"/>
				<add value="340" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="150">
			<modifiers>
				<add value="52" bonus="true" name="BLOCK"/>
				<add value="72" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="150">
			<modifiers>
				<add value="50" bonus="true" name="PARRY"/>
				<add value="340" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="275">
			<modifiers>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="67" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="58" name="phyatt_l_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="41" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="59" name="magatt_l_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="42" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="11" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="38" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="60" name="phydef_l_event_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="29" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="39" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="31" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="80" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="70" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="61" name="magdef_l_event_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="29" bonus="true" name="PARRY"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="39" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="31" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="45" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="62" name="assist_l_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="20" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="200" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="220" bonus="true" name="MAXMP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="14" bonus="true" name="BOOST_HEAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="63" name="resist_l_event_polish_set_01">
		<group id="1" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="111">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="111">
			<modifiers>
				<add value="22" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="111">
			<modifiers>
				<add value="11" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="111">
			<modifiers>
				<add value="22" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="111">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="111">
			<modifiers>
				<add value="9" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="111">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="64" name="phyatt_u_event_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="7" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="42" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="3" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="65" name="magatt_u_event_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="54" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="42" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="3" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="66" name="phydef_u_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXHP"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="67" name="magdef_u_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXHP"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="16" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="68" name="assist_u_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_HEAL"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="240" bonus="true" name="MAXHP"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="260" bonus="true" name="MAXMP"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="20" bonus="true" name="BOOST_HEAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="BOOST_HEAL"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="69" name="resist_u_event_polish_set_01">
		<group id="1" prob="111">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="111">
			<modifiers>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="111">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_RESIST"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="111">
			<modifiers>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="111">
			<modifiers>
				<add value="30" bonus="true" name="MAGICAL_RESIST"/>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="111">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="111">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="111">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="70" name="hybatt_u_event_polish_set_01">
		<group id="1" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="50" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="142">
			<modifiers>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="15" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="142">
			<modifiers>
				<add value="47" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="30" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="142">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="142">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="71" name="hybdef_u_event_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="45" bonus="true" name="EVASION"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="37" bonus="true" name="BLOCK"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="35" bonus="true" name="PARRY"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="42" bonus="true" name="EVASION"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="BLOCK"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="32" bonus="true" name="PARRY"/>
				<add value="220" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
	</polish>
	<polish id="72" name="gacha_phyatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="73" name="gacha_magatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="74" name="gacha_phydef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="75" name="gacha_magdef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="76" name="test_procreducerate_set1">
		<group id="1" prob="1000">
			<modifiers>
				<add value="10" bonus="true" name="PROC_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="77" name="phyatt_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="78" name="magatt_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="79" name="phydef_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="400">
			<modifiers>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="80" name="magdef_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="400">
			<modifiers>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="81" name="assist_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="30" bonus="true" name="BOOST_HEAL"/>
				<add value="320" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="62" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="82" name="resist_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="83" name="hybatt_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="66" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="22" bonus="true" name="PVP_DEFEND_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="84" name="hybdef_e_polish_set_02">
		<group id="1" prob="200">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="200">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="200">
			<modifiers>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PVP_ATTACK_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="85" name="abyssrank15_phyatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="86" name="abyssrank15_magatt_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="2" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="87" name="abyssrank15_phydef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="90" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="88" name="abyssrank15_magdef_e_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="6" prob="50">
			<modifiers>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="9" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="8" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="10" prob="50">
			<modifiers>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="89" name="test_pvpstat_set1">
		<group id="1" prob="1000">
			<modifiers>
				<add value="500" bonus="true" name="EVASION"/>
				<add value="500" bonus="true" name="BLOCK"/>
				<add value="500" bonus="true" name="PARRY"/>
				<add value="500" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="500" bonus="true" name="MAGICAL_RESIST"/>
				<add value="500" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="90" name="cash_fallman_phyatt_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="6" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="91" name="cash_fallman_magatt_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="46" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="26" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="92" name="cash_fallman_block_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="31" bonus="true" name="BLOCK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="93" name="cash_fallman_phydef_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="26" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="94" name="cash_fallman_magdef_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="26" bonus="true" name="MAGICAL_RESIST"/>
				<add value="55" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="13" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="95" name="phyatt_u_p_polish_set_02">
		<group id="1" prob="143">
			<modifiers>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="40" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="49" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="7" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="32" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="36" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="44" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="50" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="4" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="7" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="96" name="magatt_u_p_polish_set_02">
		<group id="1" prob="143">
			<modifiers>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="17" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="34" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="36" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="56" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="16" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="56" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="44" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="4" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="52" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="97" name="phydef_u_p_polish_set_02">
		<group id="1" prob="143">
			<modifiers>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="280" bonus="true" name="MAXHP"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="38" bonus="true" name="PARRY"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="48" bonus="true" name="EVASION"/>
				<add value="105" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="40" bonus="true" name="BLOCK"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="105" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="98" name="magdef_u_p_polish_set_02">
		<group id="1" prob="143">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="280" bonus="true" name="MAXHP"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="38" bonus="true" name="PARRY"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="48" bonus="true" name="EVASION"/>
				<add value="62" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="40" bonus="true" name="BLOCK"/>
				<add value="250" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="62" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="240" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="18" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="62" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="99" name="assist_u_p_polish_set_02">
		<group id="1" prob="143">
			<modifiers>
				<add value="24" bonus="true" name="BOOST_HEAL"/>
				<add value="260" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="38" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="65" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="53" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="260" bonus="true" name="MAXHP"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="280" bonus="true" name="MAXMP"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_HEAL"/>
				<add value="60" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_HEAL"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="100" name="resist_u_p_polish_set_02">
		<group id="1" prob="125">
			<modifiers>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="18" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="38" bonus="true" name="MAGICAL_RESIST"/>
				<add value="110" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="36" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="16" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="MAGICAL_RESIST"/>
				<add value="100" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="34" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="14" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="16" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="30" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="101" name="phyatt_e_p_polish_set_01">
		<group id="1" prob="143">
			<modifiers>
				<add value="9" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="55" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="57" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="9" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="50" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="55" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="290" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="9" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="102" name="magatt_e_p_polish_set_01">
		<group id="1" prob="143">
			<modifiers>
				<add value="68" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="42" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="44" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="64" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="64" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="50" bonus="true" name="EVASION"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="40" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="290" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="6" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="66" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="103" name="phydef_e_p_polish_set_01">
		<group id="1" prob="143">
			<modifiers>
				<add value="140" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="44" bonus="true" name="PARRY"/>
				<add value="290" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="54" bonus="true" name="EVASION"/>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="47" bonus="true" name="BLOCK"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="120" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="104" name="magdef_e_p_polish_set_01">
		<group id="1" prob="143">
			<modifiers>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="44" bonus="true" name="PARRY"/>
				<add value="290" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="54" bonus="true" name="EVASION"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="47" bonus="true" name="BLOCK"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="280" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="22" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="105" name="assist_e_p_polish_set_01">
		<group id="1" prob="143">
			<modifiers>
				<add value="28" bonus="true" name="BOOST_HEAL"/>
				<add value="300" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="2" prob="143">
			<modifiers>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="143">
			<modifiers>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="143">
			<modifiers>
				<add value="300" bonus="true" name="MAXHP"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="143">
			<modifiers>
				<add value="320" bonus="true" name="MAXMP"/>
				<add value="125" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="6" prob="143">
			<modifiers>
				<add value="26" bonus="true" name="BOOST_HEAL"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
			</modifiers>
		</group>
		<group id="7" prob="142">
			<modifiers>
				<add value="38" bonus="true" name="BOOST_HEAL"/>
				<add value="125" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
	</polish>
	<polish id="106" name="resist_e_p_polish_set_01">
		<group id="1" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="125">
			<modifiers>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="3" prob="125">
			<modifiers>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
				<add value="130" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="4" prob="125">
			<modifiers>
				<add value="44" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="20" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="125">
			<modifiers>
				<add value="22" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="40" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="6" prob="125">
			<modifiers>
				<add value="44" bonus="true" name="MAGICAL_RESIST"/>
				<add value="125" bonus="true" name="PHYSICAL_DEFENSE"/>
			</modifiers>
		</group>
		<group id="7" prob="125">
			<modifiers>
				<add value="42" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
				<add value="19" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="8" prob="125">
			<modifiers>
				<add value="21" bonus="true" name="MAGICAL_CRITICAL_RESIST"/>
				<add value="38" bonus="true" name="PHYSICAL_CRITICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="107" name="phyatt_em_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="13" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="57" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="300">
			<modifiers>
				<add value="13" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="53" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="6" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="12" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="53" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="4" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="57" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="12" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="8" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="108" name="magatt_em_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="27" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="2" prob="300">
			<modifiers>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="25" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="6" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="4" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="40" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="5" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="12" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="109" name="hybatt_em_polish_set_01">
		<group id="1" prob="100">
			<modifiers>
				<add value="12" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="8" bonus="true" name="PVP_ATTACK_RATIO"/>
			</modifiers>
		</group>
		<group id="2" prob="300">
			<modifiers>
				<add value="11" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="72" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="11" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="72" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="200">
			<modifiers>
				<add value="56" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="38" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="12" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="24" bonus="true" name="PVP_DEFEND_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="110" name="phyatt_em_polish_set_02">
		<group id="1" prob="100">
			<modifiers>
				<add value="15" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="61" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="69" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="15" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="57" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="66" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="57" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="65" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_PHYSICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="15" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="12" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
	</polish>
	<polish id="111" name="magatt_em_polish_set_02">
		<group id="1" prob="100">
			<modifiers>
				<add value="100" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="30" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="56" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="95" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="30" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="52" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="90" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="27" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="50" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="7" bonus="true" name="PVP_ATTACK_RATIO_MAGICAL"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="15" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="75" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
	</polish>
	<polish id="112" name="hybatt_em_polish_set_02">
		<group id="1" prob="100">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="90" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="64" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="13" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="52" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="12" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="38" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="60" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="80" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
		<group id="5" prob="150">
			<modifiers>
				<add value="15" bonus="true" name="PVP_ATTACK_RATIO"/>
				<add value="27" bonus="true" name="PVP_DEFEND_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="113" name="hyball_em_polish_set_02">
		<group id="1" prob="150">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="130" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="48" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="2" prob="150">
			<modifiers>
				<add value="95" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="130" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="330" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="3" prob="300">
			<modifiers>
				<add value="60" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="320" bonus="true" name="MAXHP"/>
			</modifiers>
		</group>
		<group id="4" prob="300">
			<modifiers>
				<add value="45" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
		<group id="5" prob="100">
			<modifiers>
				<add value="30" bonus="true" name="PVP_DEFEND_RATIO"/>
				<add value="10" bonus="true" name="PVP_ATTACK_RATIO"/>
			</modifiers>
		</group>
	</polish>
	<polish id="114" name="phyatt_r_s_polish_enchant_01a">
		<group id="1" prob="400">
			<modifiers>
				<add value="3" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="20" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="115" name="magatt_r_s_polish_enchant_01a">
		<group id="1" prob="400">
			<modifiers>
				<add value="18" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="5" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="116" name="phyatt_r_s_polish_enchant_02a">
		<group id="1" prob="400">
			<modifiers>
				<add value="4" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="23" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="117" name="magatt_r_s_polish_enchant_02a">
		<group id="1" prob="400">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="7" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="14" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="118" name="phyatt_r_s_polish_enchant_03a">
		<group id="1" prob="400">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="119" name="magatt_r_s_polish_enchant_03a">
		<group id="1" prob="400">
			<modifiers>
				<add value="26" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="9" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="120" name="phyatt_r_s_polish_set_01">
		<group id="1" prob="400">
			<modifiers>
				<add value="3" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="20" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="121" name="magatt_r_s_polish_set_01">
		<group id="1" prob="400">
			<modifiers>
				<add value="18" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="5" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="10" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="122" name="phyatt_r_s_polish_set_02">
		<group id="1" prob="400">
			<modifiers>
				<add value="4" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="14" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="23" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="123" name="magatt_r_s_polish_set_02">
		<group id="1" prob="400">
			<modifiers>
				<add value="22" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="7" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="14" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="124" name="phyatt_r_s_polish_set_03">
		<group id="1" prob="400">
			<modifiers>
				<add value="5" bonus="true" name="PHYSICAL_ATTACK"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="18" bonus="true" name="PHYSICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="26" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="125" name="magatt_r_s_polish_set_03">
		<group id="1" prob="400">
			<modifiers>
				<add value="26" bonus="true" name="BOOST_MAGICAL_SKILL"/>
			</modifiers>
		</group>
		<group id="2" prob="400">
			<modifiers>
				<add value="9" bonus="true" name="MAGICAL_CRITICAL"/>
			</modifiers>
		</group>
		<group id="3" prob="200">
			<modifiers>
				<add value="18" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="126" name="world_cash_phyatt_m_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="15" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="65" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="72" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="127" name="world_cash_magatt_m_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="100" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="50" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="60" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="128" name="world_cash_phydef_m_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="129" name="world_cash_magdef_m_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="130" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="340" bonus="true" name="MAXHP"/>
				<add value="50" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="130" name="cash_icestore_phyatt_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="10" bonus="true" name="PHYSICAL_ATTACK"/>
				<add value="46" bonus="true" name="PHYSICAL_CRITICAL"/>
				<add value="59" bonus="true" name="PHYSICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="131" name="cash_icestore_magatt_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="70" bonus="true" name="BOOST_MAGICAL_SKILL"/>
				<add value="23" bonus="true" name="MAGICAL_CRITICAL"/>
				<add value="46" bonus="true" name="MAGICAL_ACCURACY"/>
			</modifiers>
		</group>
	</polish>
	<polish id="132" name="cash_icestore_phydef_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="150" bonus="true" name="PHYSICAL_DEFENSE"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
	<polish id="133" name="cash_icestore_magdef_e_polish_set_01">
		<group id="1" prob="1000">
			<modifiers>
				<add value="85" bonus="true" name="BOOST_MAGICAL_SKILL_RESIST"/>
				<add value="320" bonus="true" name="MAXHP"/>
				<add value="46" bonus="true" name="MAGICAL_RESIST"/>
			</modifiers>
		</group>
	</polish>
</polishes>
