/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.MarketExchangeService;
import gameserver.services.MarketExchangeService.MarketExchangeInfo;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 */
public class ExchangeCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    public ExchangeCommand() {
        super("exchange");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.isTemporary())
            return;

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 1000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 1 seconds!");
                return;
            }
        }

        if (params.length < 1 || params[0] == "") {
            PacketSendUtility.sendMessage(player,
                "Syntax: .exchange open <price> -- opens new exchange"
                    + "\nSyntax: .exchange confirm -- confirms new exchange"
                    + "\nSyntax: .exchange cancel -- cancels new exchange"
                    + "\nSyntax: .exchange close <exchange id> -- closes exchange"
                    + "\nSyntax: .exchange list -- lists open exchanges");
            return;
        }
        else if ("open".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(player,
                    "Syntax: .exchange open <price> -- opens new exchange");
                return;
            }

            int price = 0;

            try {
                price = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
                PacketSendUtility
                    .sendMessage(player, "Error! Please enter a number for the price.");
                return;
            }

            if (price < 1000 || price > 100000) {
                PacketSendUtility.sendMessage(player, "Error! Price must be between 1,000 and 100,000.");
                return;
            }

            MarketExchangeService.getInstance().createNewExchange(player, price);

            PacketSendUtility.sendMessage(player,
                "You are about to open an exchange. Right-click the item you wish to put up for sale."
                    + "\nOnly Plumes can be put up for exchange currently.");
        }
        else if ("confirm".equalsIgnoreCase(params[0])) {
            if (!MarketExchangeService.getInstance().hasPendingExchange(player)) {
                PacketSendUtility.sendMessage(player, "You have no active exchange to confirm.");
                return;
            }

            if (!MarketExchangeService.getInstance().confirmExchange(player)) {
                PacketSendUtility
                    .sendMessage(
                        player,
                        "Failed to confirm exchange! You must select an item by right-clicking it in your inventory before you can confirm an exchange.");
                return;
            }

            PacketSendUtility.sendMessage(player,
                "Your exchange is now live! See your open exchanges by typing .exchange list");
        }
        else if ("cancel".equalsIgnoreCase(params[0])) {
            if (!MarketExchangeService.getInstance().hasPendingExchange(player)) {
                PacketSendUtility.sendMessage(player, "You have no active exchange to cancel.");
                return;
            }

            MarketExchangeService.getInstance().cancelExchange(player);

            PacketSendUtility.sendMessage(player, "The current exchange has been cancelled!");
        }
        else if ("close".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(player,
                    "Syntax: .exchange close <exchange id> -- closes exchange");
                return;
            }

            int id = 0;

            try {
                id = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Please enter a number for the exchange id.");
                return;
            }

            if (MarketExchangeService.getInstance().closeExchange(player, id)) {
                PacketSendUtility.sendMessage(player,
                    "You have successfully closed the exchange and your item has been returned.");
            }
            else {
                PacketSendUtility.sendMessage(player,
                    "No exchange was closed. Make sure you typed the correct exchange id.");
            }
        }
        else if ("list".equalsIgnoreCase(params[0])) {
            Collection<MarketExchangeInfo> list = MarketExchangeService.getInstance()
                .getOpenExchanges(player);

            if (list.isEmpty()) {
                PacketSendUtility.sendMessage(player, "You currently have no open exchanges.");
            }
            else {
                StringBuilder sb = new StringBuilder();

                sb.append("=== OPEN EXCHANGES ===\n");

                for (MarketExchangeInfo exchange : list) {
                    sb.append("[");
                    sb.append(exchange.getId());
                    sb.append("] ");

                    if (exchange.getTemperance() > 0) {
                        sb.append("+");
                        sb.append(exchange.getTemperance());
                        sb.append(" ");
                    }

                    if (exchange.getEnchant() > 0) {
                        sb.append("+");
                        sb.append(exchange.getEnchant());
                        sb.append(" ");
                    }

                    sb.append("[item:");
                    sb.append(exchange.getItemId());
                    sb.append("]");

                    sb.append(" (");
                    sb.append(exchange.getCreator());
                    sb.append(")");

                    sb.append(" for ");
                    sb.append(exchange.getPrice());
                    sb.append(" Might\n");
                }

                sb.append("=== END OPEN EXCHANGES ===");

                PacketSendUtility.sendMessage(player, sb.toString());
            }
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}