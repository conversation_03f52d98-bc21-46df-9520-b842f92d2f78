/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.ShopDAO;
import gameserver.model.ShopCategory;
import gameserver.model.ShopItem;
import gameserver.model.ShopItemType;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.CashShopManager;
import gameserver.services.ItemService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
public class Shop extends AdminCommand {

    public Shop() {
        super("shop");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //shop <add | refresh>");
            return;
        }

        if (params[0].startsWith("add")) {
            if (params.length < 7) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //shop add <category> <item id> <count> <price> <type> <name>\n"
                        + "Syntax: category is the name - use Sales to add to Sales Ranking\n"
                        + "Syntax: type can be NORMAL, NEW or HOT");
                return;
            }

            List<ShopCategory> categories = DAOManager.getDAO(ShopDAO.class).getCategoryList();

            int categoryId = -1;
            for (ShopCategory category : categories)
                if (category.getName().equalsIgnoreCase(params[1]))
                    categoryId = category.getId();

            if (categoryId == -1) {
                PacketSendUtility.sendMessage(admin, "Error! There is no category called "
                    + params[1] + ".");
                return;
            }

            if (ItemService.getItemTemplate(Integer.parseInt(params[2])) == null) {
                PacketSendUtility.sendMessage(admin,
                    "Error! The specified item id is not a valid item.");
                return;
            }

            String name = "";
            for (int i = 6; i < params.length; i++)
                if (name.length() > 0)
                    name += " " + params[i];
                else
                    name += params[i];

            ShopItem shopItem = new ShopItem(0);

            shopItem.setItemId(Integer.parseInt(params[2]));
            shopItem.setCount(Integer.parseInt(params[3]));
            shopItem.setPrice(Integer.parseInt(params[4]));
            shopItem.setType(ShopItemType.valueOf(params[5].toUpperCase()));
            shopItem.setName(name);

            DAOManager.getDAO(ShopDAO.class).addItem(categoryId, shopItem);
            PacketSendUtility.sendMessage(admin, "The item has been added to the shop!\n"
                + "Please use //shop refresh to view changes");
        }
        else if (params[0].startsWith("refresh")) {
            PacketSendUtility.sendMessage(admin, "Refreshing shop data from database...");

            long timer = System.currentTimeMillis();
            CashShopManager.getInstance().loadShopData();
            timer = System.currentTimeMillis() - timer;

            PacketSendUtility.sendMessage(admin, "Shop data refreshed in " + timer + " ms.");
        }
    }
}
