/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 */
public class SpawnRecharger extends AdminCommand {
    /**
     * Constructor
     */
    public SpawnRecharger() {
        super("spawnrecharger");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //spawnrecharger <radius> -- distance in meters to recharge within"
                        + "\nOR Syntax: //spawnrecharger delete -- deletes the currently targetted recharger");
            return;
        }

        if (params[0].equalsIgnoreCase("delete")) {
            if (!(admin.getTarget() instanceof Npc)
                || ((Npc) admin.getTarget()).getNpcId() != 730397) {
                PacketSendUtility.sendMessage(admin,
                    "Please target a recharger to use this command!");
                return;
            }

            ((Npc) admin.getTarget()).getController().onDelete();
            PacketSendUtility.sendMessage(admin, "Deleted recharger!");

            return;
        }

        int mapId = admin.getWorldId();
        int instanceId = admin.getInstanceId();
        int npcId = 730397;
        float x = admin.getX();
        float y = admin.getY();
        float z = admin.getZ();

        int radius = Integer.parseInt(params[0]);

        Npc npc = SpawnEngine.getInstance().spawnRecharger(mapId, instanceId, npcId, x, y, z,
            radius);

        if (npc == null) {
            PacketSendUtility.sendMessage(admin, "Failed to spawn the Recharger!");
        }
        else {
            PacketSendUtility.sendMessage(admin, "A Recharger has been spawned!");
        }
    }
}
