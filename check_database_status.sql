-- ====================================================================
-- DATABASE STATUS CHECK
-- ====================================================================
-- Run this script to see what tables exist in your database
-- and what columns they have.
-- ====================================================================

-- Change to your database name
USE `not-aion`;

-- Check if shop tables exist
SELECT 'Checking if shop_purchases table exists...' AS status;
SELECT COUNT(*) AS table_exists FROM information_schema.tables 
WHERE table_schema = 'not-aion' AND table_name = 'shop_purchases';

SELECT 'Checking if shop_removals table exists...' AS status;
SELECT COUNT(*) AS table_exists FROM information_schema.tables 
WHERE table_schema = 'not-aion' AND table_name = 'shop_removals';

-- If tables exist, show their structure
SELECT 'shop_purchases table structure (if exists):' AS status;
SHOW CREATE TABLE shop_purchases;

SELECT 'shop_removals table structure (if exists):' AS status;
SHOW CREATE TABLE shop_removals;

-- Show all tables in database
SELECT 'All tables in database:' AS status;
SHOW TABLES;

-- ====================================================================
-- INTERPRETATION:
-- ====================================================================
-- If table_exists = 0: Table doesn't exist
-- If table_exists = 1: Table exists
-- 
-- If you get errors running SHOW CREATE TABLE, the tables don't exist
-- If you see the table structures, check if column names match:
-- 
-- shop_purchases should have: id, char_id, item_id, quantity, gift, gifter, added
-- shop_removals should have: id, itemUniqueId, itemOwner, amount, removed
-- ====================================================================
