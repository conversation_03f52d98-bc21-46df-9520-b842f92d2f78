/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.SpawnDAO;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.DecorationObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnGroup;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import java.util.Iterator;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class Decor extends AdminCommand {
    public Decor() {
        super("decor");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SPAWN) {
            PacketSendUtility.sendMessage(admin,
                "You do not have enough rights to execute this command");
            return;
        }

        if (params.length < 1) {
            syntax(admin);
            return;
        }

        if ("spawn".startsWith(params[0].toLowerCase()) && params.length >= 2) {
            int templateId = Integer.parseInt(params[1]);

            if (templateId >= ********* && templateId <= *********)
                templateId = (templateId - *********) + 3000000;

            float zOffset = 0;
            if (params.length >= 3)
                zOffset = Float.parseFloat(params[2]);

            SpawnTemplate spawn = new SpawnTemplate(admin.getX(), admin.getY(), admin.getZ()
                + zOffset, admin.getHeading(), 0, 0, 0);

            spawn.setSpawnGroup(new SpawnGroup(admin.getWorldId(), templateId, 295, 1));

            DecorationObject object = (DecorationObject) SpawnEngine.getInstance().spawnObject(
                spawn, admin.getInstanceId());

            int spawnId = 0;
            // if (admin.getInstanceId() == 1 && admin.getAccessLevel() >= 5) {
            spawnId = DAOManager.getDAO(SpawnDAO.class).addSpawn(templateId, admin.getObjectId(),
                null, true, spawn.getWorldId(), spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), object.getObjectId(), 0);
            // }

            PacketSendUtility.sendMessage(admin, "Spawned decoration object " + templateId
                + " with object id " + object.getObjectId() + " and DB spawnId #" + spawnId);
        }
        else if ("delete".startsWith(params[0].toLowerCase()) && params.length >= 2) {
            int objectId = Integer.parseInt(params[1]);

            AionObject obj = World.getInstance().findAionObject(objectId);
            if (obj instanceof DecorationObject) {
                SpawnTemplate spawn = ((DecorationObject) obj).getSpawn();

                int spawnId = DAOManager.getDAO(SpawnDAO.class)
                    .isInDB(((DecorationObject) obj).getTemplateId(),
                        ((DecorationObject) obj).getWorldId(), spawn.getX(), spawn.getY(),
                        spawn.getZ());

                if (spawnId != 0)
                    DAOManager.getDAO(SpawnDAO.class).deleteSpawn(spawnId);

                ((DecorationObject) obj).getController().delete();
                PacketSendUtility.sendMessage(admin, "Deleted decoration object");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Invalid object id specified!");
            }
        }
        else if ("wipeall".startsWith(params[0].toLowerCase())) {
            float distance = -1F;

            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //decor wipeall <range | all>");
                return;
            }

            if (!params[1].equalsIgnoreCase("all"))
                distance = Float.parseFloat(params[1]);

            if (admin.getAccessLevel() <= 2 && (distance < 0F || distance > 100F)) {
                PacketSendUtility.sendMessage(admin,
                    "WARNING: Wipe distance reduced to 100m due to access level.");
                distance = 100F;
            }

            for (Iterator<AionObject> it = admin.getWorldMapInstance().getObjects().iterator(); it
                .hasNext();) {
                AionObject obj = it.next();

                if (obj instanceof DecorationObject) {
                    if (distance > 0
                        && !MathUtil.isIn3dRange(admin, (DecorationObject) obj, distance))
                        continue;

                    SpawnTemplate spawn = ((DecorationObject) obj).getSpawn();

                    int spawnId = DAOManager.getDAO(SpawnDAO.class).isInDB(
                        ((DecorationObject) obj).getTemplateId(),
                        ((DecorationObject) obj).getWorldId(), spawn.getX(), spawn.getY(),
                        spawn.getZ());

                    if (spawnId != 0)
                        DAOManager.getDAO(SpawnDAO.class).deleteSpawn(spawnId);

                    ((DecorationObject) obj).getController().delete();
                }
            }
        }
        else if ("wipevertical".startsWith(params[0].toLowerCase())) {
            float distance = -1F;

            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //decor wipevertical <range>");
                return;
            }

            distance = Float.parseFloat(params[1]);

            if (admin.getAccessLevel() <= 2 && (distance < 0F || distance > 100F)) {
                PacketSendUtility.sendMessage(admin,
                    "WARNING: Wipe distance reduced to 100m due to access level.");
                distance = 100F;
            }

            for (Iterator<AionObject> it = admin.getWorldMapInstance().getObjects().iterator(); it
                .hasNext();) {
                AionObject obj = it.next();

                if (obj instanceof DecorationObject) {
                    if (distance > 0
                        && !MathUtil.isInRange(admin, (DecorationObject) obj, distance))
                        continue;

                    SpawnTemplate spawn = ((DecorationObject) obj).getSpawn();

                    int spawnId = DAOManager.getDAO(SpawnDAO.class).isInDB(
                        ((DecorationObject) obj).getTemplateId(),
                        ((DecorationObject) obj).getWorldId(), spawn.getX(), spawn.getY(),
                        spawn.getZ());

                    if (spawnId != 0)
                        DAOManager.getDAO(SpawnDAO.class).deleteSpawn(spawnId);

                    ((DecorationObject) obj).getController().delete();
                }
            }
        }
        else {
            syntax(admin);
        }
    }

    private void syntax(Player admin) {
        PacketSendUtility
            .sendMessage(
                admin,
                "Syntax: //decor <spawn | delete | wipeall>"
                    + "\nSyntax: //decor spawn <templateId> [zOffset] -- spawns the decoration at your position"
                    + "\nSyntax: //decor delete <objectId> -- deletes the decoration"
                    + "\nSyntax: //decor wipeall <range | all> -- deletes all (or within range) decorations on map"
                    + "\nSyntax: //decor wipevertical <range> -- deletes all objects within ground distance");
    }
}
