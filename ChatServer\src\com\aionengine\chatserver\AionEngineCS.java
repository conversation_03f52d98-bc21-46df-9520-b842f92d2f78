/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.aionengine.chatserver;

import org.apache.log4j.Logger;

import com.aionengine.chatserver.ChatServer;

public class AionEngineCS
{
	private static final Logger _log = Logger.getLogger(AionEngineCS.class);

	/**
	 * by <PERSON><PERSON>ri
	 */
	public static void infoCS()
	{
		_log.info("-------------------------------------------------------------");
		_log.info("	 			  This Is A Community Ran Project				");
		_log.info("		        For the Community, By The Community		 		");
		_log.info("				      	Log In,Play,Enjoy!                      ");
		_log.info("																");
		_log.info("-------------------------------------------------------------");
		_log.info("			        											");
		_log.info("																");
		_log.info("					-Aion X Emu Developers- 					");
		_log.info("	                						                    ");
		_log.info("	        Crash Override | Bootyroast | LiquidIce             ");
		_log.info("                                                             ");
		_log.info("			       More Will Be Added Soon...					");
		_log.info("                                                             ");
		_log.info("											                    ");
		_log.info("                                                             ");
		_log.info("             							                    ");
		_log.info("                                                             ");
		_log.info("                                                             ");
		_log.info("																");
		_log.info("-------------------------------------------------------------");
		_log.info("																");	
		_log.info("          :::     :::::::::::  ::::::::  ::::    :::  		");
		_log.info("        :+: :+:       :+:     :+:    :+: :+:+:   :+:  		");
		_log.info("       +:+   +:+      +:+     +:+    +:+ :+:+:+  +:+  		");
		_log.info("      +#++:++#++:     +#+     +#+    +:+ +#+ +:+ +#+  		");
		_log.info("      +#+     +#+     +#+     +#+    +#+ +#+  +#+#+#  		");
		_log.info("      #+#     #+#     #+#     #+#    #+# #+#   #+#+#  		");
		_log.info("      ###     ### ###########  ########  ###    ####  		");
		_log.info("																");
		_log.info("						    :::    :::							");
		_log.info("							:+:    :+:							");
		_log.info("							 +:+  +:+							");
		_log.info("							  +#++:+							");
		_log.info("							 +#+  +#+     						");
		_log.info("							#+#    #+#							");
		_log.info("							###    ###							");
		_log.info("																");
		_log.info("  		    :::::::::: ::::    ::::: :::    :::				");
		_log.info("    		    :+:        +:+:+: :+:+:+ :+:    :+:				");
		_log.info(" 		    +:+        +:+ +:+:+ +:+ +:+    +:+				");
		_log.info("   			+#++:++#   +#+  +:+  +#+ +#+    +:+				");
		_log.info("  			+#+        +#+       +#+ +#+    +#+				");
		_log.info("				#+#        #+#       #+# #+#    #+#				");
		_log.info(" 			########## ###       ###  ########				");
		_log.info("-------------------------------------------------------------");
	}
}