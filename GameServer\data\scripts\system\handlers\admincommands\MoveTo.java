/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.InstanceService;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.WorldMapInstance;

/**
 * Admin moveto command
 * 
 * <AUTHOR>
 */

public class MoveTo extends AdminCommand {

    /**
     * Constructor.
     */

    public MoveTo() {
        super("moveto");
    }

    /**
     * {@inheritDoc}
     */

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_MOVETO) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params == null || params.length < 3) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //moveto <world Id> <X> <Y> <Z> [instance]"
                    + "\nOR Syntax: //moveto <X> <Y> <Z>");
            return;
        }

        int worldId;
        float x, y, z;

        try {
            if (params.length == 3) {
                worldId = admin.getWorldId();
                x = Float.parseFloat(params[0]);
                y = Float.parseFloat(params[1]);
                z = Float.parseFloat(params[2]);
            }
            else {
                worldId = Integer.parseInt(params[0]);
                x = Float.parseFloat(params[1]);
                y = Float.parseFloat(params[2]);
                z = Float.parseFloat(params[3]);
            }
        }
        catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(admin, "All the parameters should be numbers");
            return;
        }

        if (DataManager.WORLD_MAPS_DATA.getTemplate(worldId) == null) {
            PacketSendUtility.sendMessage(admin, "Map id " + worldId
                + " either does not exist or hasn't been activated on the server.");
            return;
        }

        if (params.length >= 5) {
            WorldMapInstance instance = InstanceService.getNextBgInstance(worldId);
            if (instance == null) {
                PacketSendUtility.sendMessage(admin,
                    "An error occured while creating the instance! Check the worldId.");
                return;
            }

            InstanceService.registerPlayerWithInstance(instance, admin);
            TeleportService.teleportTo(admin, worldId, instance.getInstanceId(), x, y, z, 0);
            PacketSendUtility.sendMessage(admin, "Teleported to " + x + " " + y + " " + z + " ["
                + worldId + " - " + instance.getInstanceId() + "]");
        }
        else {
            TeleportService.teleportTo(admin, worldId, x, y, z, 0);
            PacketSendUtility.sendMessage(admin, "Teleported to " + x + " " + y + " " + z + " ["
                + worldId + "]");
        }
    }
}
