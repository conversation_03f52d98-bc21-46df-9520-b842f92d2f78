/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package loginserver.network.aion.serverpackets;

import java.nio.ByteBuffer;

import loginserver.network.aion.AionConnection;
import loginserver.network.aion.AionServerPacket;
import loginserver.network.aion.SecurityCode;
import loginserver.network.aion.SessionKey;

/**
 * <AUTHOR>
 */
public class SM_PINCODE extends AionServerPacket{
	/**
	 * accountId is part of session key - its used for security purposes
	 */
	private final int	accountId;

	private final int	pinEntry;

	/**
	 * Constructs new instance of <tt>SM_LOGIN_OK</tt> packet.
	 * 
	 * @param key
	 *            session key
	 */
	public SM_PINCODE(SessionKey key, SecurityCode securityCode) {
		super(0x0A);

		this.accountId = key.accountId;
		this.pinEntry = securityCode.entry;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void writeImpl(AionConnection con, ByteBuffer buf) {
		writeC(buf, getOpcode());
		writeD(buf, accountId);
		writeC(buf, pinEntry);
		writeD(buf, 0); // unk
	}
}
