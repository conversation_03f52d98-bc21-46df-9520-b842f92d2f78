/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;
import gameserver.world.WorldMap;
import gameserver.world.WorldMapInstance;

/**
 * <AUTHOR>
 */
public class InstanceCommand extends AdminCommand {

    public InstanceCommand() {
        super("instance");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        WorldMap map = World.getInstance().getWorldMap(admin.getWorldId());

        if (params.length == 0) {
            PacketSendUtility.sendMessage(admin, "Syntax: //instance <id> -- go to instance by id"
                + "\n" + buildInstanceSummary(map));
            return;
        }

        int instanceId = Integer.parseInt(params[0]);

        boolean exists = false;

        for (WorldMapInstance instance : map.getInstances()) {
            if (instance.getInstanceId() == instanceId) {
                exists = true;
                break;
            }
        }

        if (!exists) {
            PacketSendUtility.sendMessage(admin,
                "The specified instance does not exist. You can only go to existing instances.");
            return;
        }

        TeleportService.changeChannel(admin, instanceId - 1);

        PacketSendUtility.sendMessage(admin, "You have moved to instance " + instanceId);
    }

    private String buildInstanceSummary(WorldMap map) {
        StringBuilder sb = new StringBuilder();

        sb.append("[ World " + map.getMapId() + " has " + map.getInstances().size()
            + " instances ]");

        for (WorldMapInstance instance : map.getInstances()) {
            sb.append("\nInstance " + instance.getInstanceId() + " has "
                + instance.getPlayersCount() + " players and " + instance.getObjectsCount()
                + " total objects.");
        }

        return sb.toString();
    }
}
