/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.model.LegionManor;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.siege.SiegeSpawnLocationTemplate;
import gameserver.services.ArenaService;
import gameserver.services.SiegeService;
import gameserver.services.TeleportService;
import gameserver.skillengine.effect.EffectId;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.chathandlers.UserCommand;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 */
public class VillageCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    public VillageCommand() {
        super("village");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (!player.isLegionMember()) {
            message(player, "Only members of a Legion that owns a Village can use this command.");
            return;
        }

        LegionManor manor = SiegeService.getInstance().getManorByLegion(player.getLegion());

        if (manor == null) {
            message(player, "Only members of a Legion that owns a Village can use this command.");
            return;
        }
        else if (manor.getRace().getRaceId() != player.getCommonData().getRace().getRaceId()) {
            message(player,
                "Your Legion's Village is not of the same race as you. You cannot go there.");
            return;
        }

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 60 * 1000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 60 seconds!");
                return;
            }
        }

        if (!enterManor(player, manor)) {
            message(player, "You cannot go to the Village while in BG, FFA, or any form of combat.");
        }
        else {
            lastExecute.put(player.getObjectId(), System.currentTimeMillis());
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }

    private boolean enterManor(final Player player, final LegionManor manor) {
        if (player == null || manor == null || player.isInCombatLong()
            || player.getBattleground() != null || ArenaService.getInstance().isInArena(player))
            return false;

        player.getEffectController().setAbnormal(EffectId.PETRIFICATION.getEffectId());
        player.getEffectController().updatePlayerEffectIcons();
        player.getEffectController().broadCastEffects();

        final ActionObserver observer = new ActionObserver(ObserverType.ATTACKED) {
            @Override
            public void attacked(Creature creature) {
                if (player.getController().hasActiveTask(TaskId.HALL)) {
                    player.getController().cancelTask(TaskId.HALL);

                    player.getEffectController()
                        .unsetAbnormal(EffectId.PETRIFICATION.getEffectId());
                    player.getEffectController().updatePlayerEffectIcons();
                    player.getEffectController().broadCastEffects();
                }
            }
        };

        player.getObserveController().attach(observer);

        player.getController().addTask(TaskId.HALL,
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    player.getObserveController().removeObserver(observer);

                    player.getEffectController()
                        .unsetAbnormal(EffectId.PETRIFICATION.getEffectId());
                    player.getEffectController().updatePlayerEffectIcons();
                    player.getEffectController().broadCastEffects();

                    SiegeSpawnLocationTemplate pos = manor.getEntry();
                    TeleportService.teleportTo(player, manor.getWorldId(), 1, pos.getX(),
                        pos.getY(), pos.getZ(), TeleportService.TELEPORT_BEAM_DELAY);
                }
            }, 5 * 1000));

        return true;
    }
}