/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.MotionDAO;
import gameserver.model.gameobjects.player.Motion;
import gameserver.model.gameobjects.player.Player;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5MotionDAO extends MotionDAO {
    private static final String LOAD_QUERY = "SELECT `motion_idle`, `motion_run`, `motion_jump`, `motion_rest`, `motion_shop` FROM `motions` WHERE `player_id`=?";
    private static final String INSERT_QUERY = "INSERT INTO `motions` (`player_id`, `motion_idle`, `motion_run`, `motion_jump`, `motion_rest`, `motion_shop`) VALUES (?,?,?,?,?,?)";
    private static final String UPDATE_QUERY = "UPDATE `motions` SET `motion_idle`=?, `motion_run`=?, `motion_jump`=?, `motion_rest`=?, `motion_shop`=? WHERE `player_id`=?";
    private static final String CHECK_QUERY = "SELECT `player_id` FROM `motions` WHERE `player_id`=?";
    private static final String REPLACE_QUERY = "REPLACE INTO `motions` (`player_id`, `motion_idle`, `motion_run`, `motion_jump`, `motion_rest`, `motion_shop`) VALUES (?,?,?,?,?,?)";

    private static final Logger log = Logger.getLogger(MySQL5PlayerMotionListDAO.class);

    @Override
    public Map<Integer, Motion> getMotions(final int playerId) {
        final Map<Integer, Motion> ml = new HashMap<Integer, Motion>();

        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(LOAD_QUERY);
            stmt.setInt(1, playerId);
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                ml.put(1, Motion.getMotionById(rset.getInt("motion_idle")));
                ml.put(2, Motion.getMotionById(rset.getInt("motion_run")));
                ml.put(3, Motion.getMotionById(rset.getInt("motion_jump")));
                ml.put(4, Motion.getMotionById(rset.getInt("motion_rest")));
                ml.put(5, Motion.getMotionById(rset.getInt("motion_shop")));
            }
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return ml;
    }

    @Override
    public boolean saveMotions(Player player) {
        final int playerId = player.getObjectId();
        final Map<Integer, Motion> motions = player.getMotions();

        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            // PreparedStatement stmt = con.prepareStatement(CHECK_QUERY);
            // stmt.setInt(1, playerId);
            // ResultSet rset = stmt.executeQuery();
            // if (!rset.next()) {
            PreparedStatement stmt = con.prepareStatement(REPLACE_QUERY);// con.prepareStatement(INSERT_QUERY);
            stmt.setInt(1, playerId);
            stmt.setInt(2, motions.get(1) != null ? motions.get(1).getMotionId() : 0);
            stmt.setInt(3, motions.get(2) != null ? motions.get(2).getMotionId() : 0);
            stmt.setInt(4, motions.get(3) != null ? motions.get(3).getMotionId() : 0);
            stmt.setInt(5, motions.get(4) != null ? motions.get(4).getMotionId() : 0);
            stmt.setInt(6, motions.get(5) != null ? motions.get(5).getMotionId() : 0);

            stmt.execute();
            /*
             * } else { PreparedStatement stmt2 = con.prepareStatement(UPDATE_QUERY); stmt2.setInt(1, motions.get(1) !=
             * null ? motions.get(1).getMotionId() : 0); stmt2.setInt(2, motions.get(2) != null ?
             * motions.get(2).getMotionId() : 0); stmt2.setInt(3, motions.get(3) != null ? motions.get(3).getMotionId()
             * : 0); stmt2.setInt(4, motions.get(4) != null ? motions.get(4).getMotionId() : 0); stmt2.setInt(5,
             * motions.get(5) != null ? motions.get(5).getMotionId() : 0); stmt2.setInt(6, playerId); stmt2.execute(); }
             */
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }

}
