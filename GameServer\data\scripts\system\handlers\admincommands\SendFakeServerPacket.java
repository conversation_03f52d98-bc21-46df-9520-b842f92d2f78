/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_CUSTOM_PACKET;
import gameserver.network.aion.serverpackets.SM_CUSTOM_PACKET.PacketElementType;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.regex.Pattern;

/**
 * This server command is used for creating and sending custom packets from server to client. It's used in development
 * purpose.<br>
 * <b>command name: //fsc</b></br> <b>params:</b>
 * <ul>
 * <li>packet id (it's one byte) - maybe in dec format (for example 227), but may be also in hex format (for example
 * 0xE3)</li>
 * <li>package format string - string containing with letters: d (represents writeD()), h (represents writeH()), c
 * (represents writeC()), f (represents writeF()), e (represents write DF()), q (represents writeQ()), s (represents
 * writeS())</li>
 * <li>list of data - here goes all data for corresponding to proper format parts.</li>
 * </ul>
 * Example:<br>
 * //fsc 0xD8 cdds 8 50 80 someText - will send packet with id 0xD8 (subids will be added automaticaly) then will be
 * sent one byte - 8, later two ints -50 and 80 and at the end a String - someText
 * 
 * <AUTHOR>
 */

public class SendFakeServerPacket extends AdminCommand {
    public SendFakeServerPacket() {
        super("fsc");
    }

    /**
     * {@inheritDoc}
     */

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SENDFAKESERVERPACKET) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length < 3) {
            PacketSendUtility.sendMessage(admin, "Syntax: //fsc <opcode> <format> <elements ...>\n"
                + "OR syntax: //fsc <opcode> <extraOpcode> <format> <elements ...>");
            return;
        }

        int id = Integer.decode(params[0]);
        SM_CUSTOM_PACKET packet = new SM_CUSTOM_PACKET(id);

        String expression = "[-+]?[0-9]*\\.?[0-9]+$";
        CharSequence inputStr = params[1];
        Pattern pattern = Pattern.compile(expression);

        String format = "";

        boolean hasExtra = false;

        if (pattern.matcher(inputStr).matches()) {
            packet.setExtraOpcode(Integer.parseInt(params[1]));
            format = params[2];
            hasExtra = true;
        }
        else {
            format = params[1];
        }

        int i = 0;
        for (char c : format.toCharArray()) {
            packet.addElement(PacketElementType.getByCode(c), params[(hasExtra ? 3 : 2) + i]);
            i++;
        }

        PacketSendUtility.sendPacket(admin, packet);
    }
}
