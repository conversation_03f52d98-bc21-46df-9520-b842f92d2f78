/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package loginserver.network.aion.clientpackets;

import java.nio.ByteBuffer;
import java.security.GeneralSecurityException;

import javax.crypto.Cipher;

import loginserver.controller.AccountController;
import loginserver.dao.AccessLogDAO;
import loginserver.network.aion.AionAuthResponse;
import loginserver.network.aion.AionClientPacket;
import loginserver.network.aion.AionConnection;
import loginserver.network.aion.AionConnection.State;
import loginserver.network.aion.SecurityCode;
import loginserver.network.aion.SessionKey;
import loginserver.network.aion.serverpackets.SM_LOGIN_BLOCKED;
import loginserver.network.aion.serverpackets.SM_LOGIN_FAIL;
import loginserver.network.aion.serverpackets.SM_LOGIN_OK;
import loginserver.network.aion.serverpackets.SM_PINCODE;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR> KID
 */
public class CM_LOGIN extends AionClientPacket{
	/**
	 * Logger for this class.
	 */
	private static final Logger	log	= Logger.getLogger(CM_LOGIN.class);

	/**
	 * byte array contains encrypted login and password.
	 */
	private byte[]				data;
	private byte[]				data2;

	/**
	 * Constructs new instance of <tt>CM_LOGIN </tt> packet.
	 * 
	 * @param buf
	 * @param client
	 */
	public CM_LOGIN(ByteBuffer buf, AionConnection client) {
		super(buf, client, 0x0b);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void readImpl() {
		readD();
		if(getRemainingBytes() >= 128) {
			data = readB(128);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	protected void runImpl() {
		if(data == null)
			return;

		byte[] decrypted;
		try {
			Cipher rsaCipher = Cipher.getInstance("RSA/ECB/nopadding");
			rsaCipher.init(Cipher.DECRYPT_MODE, getConnection().getRSAPrivateKey());
			decrypted = rsaCipher.doFinal(data, 0, 128);
		}
		catch(GeneralSecurityException e) {
			log.warn("Error while decripting data on user auth." + e, e);
			sendPacket(new SM_LOGIN_FAIL(AionAuthResponse.SYSTEM_ERROR));
			return;
		}

		String user = new String(decrypted, 64, 32).trim().toLowerCase();
		String password = new String(decrypted, 96, 32).trim();

		int ncotp = decrypted[0x7c];
		ncotp |= decrypted[0x7d] << 8;
		ncotp |= decrypted[0x7e] << 16;
		ncotp |= decrypted[0x7f] << 24;

		log.debug("AuthLogin: " + user + " pass: " + password + " ncotp: " + ncotp);

		AionConnection client = getConnection();
		AionAuthResponse response = AccountController.login(user, password, client);

		switch(response) {
			case AUTHED:
				client.setState(State.AUTHED_LOGIN);
				client.setSessionKey(new SessionKey(client.getAccount()));

				if(client.getAccount().getPin() >= 100000 && client.getAccount().getPin() <= 999999) {
					String pin = String.valueOf(client.getAccount().getPin());

					int entry = Integer.valueOf(pin.substring(0, 2));
					int pincode = Integer.valueOf(pin.substring(2, 6));

					client.setSecurityCode(new SecurityCode(client.getAccount(), entry, pincode));
					client.sendPacket(new SM_PINCODE(client.getSessionKey(), client.getSecurityCode()));
				}
				else {
					client.sendPacket(new SM_LOGIN_OK(client.getSessionKey()));
				}
				break;

			case BAN_ACCOUNT:
				client.close(new SM_LOGIN_BLOCKED(8), true);
				break;

			default:
				client.close(new SM_LOGIN_FAIL(response), true);
				break;
		}

		DAOManager.getDAO(AccessLogDAO.class).logAccess(user, response, client.getIP());
	}
}
