/*
 * This file is part of Aion X EMU <aionxemu.com>.
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.aionengine.chatserver.network.aion.clientpackets;

import org.jboss.netty.buffer.ChannelBuffer;

import com.aionengine.chatserver.network.aion.AbstractClientPacket;
import com.aionengine.chatserver.network.aion.serverpackets.SM_INIT_CONNECTION;
import com.aionengine.chatserver.network.netty.handler.ClientChannelHandler;

/**
 * <AUTHOR>
 */
public class CM_INIT_CONNECTION extends AbstractClientPacket
{
	private int	unk1;
	private int	unk2;

	public CM_INIT_CONNECTION(ChannelBuffer channelBuffer, ClientChannelHandler gameChannelHandler)
	{
		super(channelBuffer, gameChannelHandler, 0x30);
	}

	@Override
	protected void readImpl()
	{
		readC();
		readH();
		unk1 = readD();
		unk2 = readD();
		readD();
	}

	@Override
	protected void runImpl()
	{
		clientChannelHandler.sendPacket(new SM_INIT_CONNECTION(unk1));
	}
}