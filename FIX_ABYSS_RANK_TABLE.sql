-- ====================================================================
-- FIX ABYSS_RANK TABLE - ADD MISSING GLORY COLUMNS
-- ====================================================================
-- This script adds the missing glory-related columns to the abyss_rank table
-- that the MySQL5AbyssRankDAO.java code expects.
-- ====================================================================

-- Change to your database name
USE `not-aion`;

-- Add missing glory columns to abyss_rank table
ALTER TABLE `abyss_rank` 
ADD COLUMN `daily_glory` int(11) NOT NULL DEFAULT '0' AFTER `ap`,
ADD COLUMN `weekly_glory` int(11) NOT NULL DEFAULT '0' AFTER `daily_glory`,
ADD COLUMN `glory` int(11) NOT NULL DEFAULT '0' AFTER `weekly_glory`,
ADD COLUMN `last_glory` int(11) NOT NULL DEFAULT '0' AFTER `last_ap`;

-- Verify the columns were added
SELECT 'abyss_rank table structure after adding glory columns:' AS status;
DESCRIBE abyss_rank;

-- ====================================================================
-- COLUMNS ADDED SUCCESSFULLY!
-- ====================================================================
-- The following columns have been added to abyss_rank table:
-- - daily_glory (int(11), default 0)
-- - weekly_glory (int(11), default 0) 
-- - glory (int(11), default 0)
-- - last_glory (int(11), default 0)
--
-- These match exactly what the MySQL5AbyssRankDAO.java code expects.
-- Your AbyssRankingService errors should now be resolved!
-- ====================================================================
