/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package loginserver.network.gameserver.clientpackets;

import java.nio.ByteBuffer;

import loginserver.network.gameserver.GsClientPacket;
import loginserver.network.gameserver.GsConnection;
import loginserver.network.gameserver.serverpackets.SM_RESPONSE_HEARTBEAT;

/**
 * 
 * <AUTHOR>
 */
public class CM_REQUEST_HEARTBEAT extends GsClientPacket{

	/**
	 * Constructor.
	 * 
	 * @param buf
	 * @param client
	 */
	public CM_REQUEST_HEARTBEAT(ByteBuffer buf, GsConnection client) {
		super(buf, client, 0x08);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void readImpl() {
		// nothing to read
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void runImpl() {
		getConnection().sendPacket(new SM_RESPONSE_HEARTBEAT());
	}
}
