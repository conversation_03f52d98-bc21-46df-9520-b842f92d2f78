/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * <AUTHOR>
 */

public class Return extends AdminCommand {

    public Return() {
        super("return");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_MOVETOME) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        Player player = null;

        if (params.length > 0) {
            player = World.getInstance().findPlayer(Util.convertName(params[0]));

            if (player == null) {
                PacketSendUtility.sendMessage(admin, "Couldn't find player " + params[0] + "!");
                return;
            }
        }
        else {
            final VisibleObject target = admin.getTarget();

            if (target == null || !(target instanceof Player)) {
                PacketSendUtility.sendMessage(admin, "Please select a player.");
                return;
            }

            player = (Player) target;
        }

        TeleportService.moveToBindLocation(player, true);

        PacketSendUtility.sendMessage(admin, "Returned player " + player.getName()
            + " to his bind point");
        PacketSendUtility.sendMessage(player, "You have been returned by " + admin.getName() + ".");
    }
}
