-- ====================================================================
-- FINAL SHOP TABLES FIX - EXACT MATCH TO JAVA CODE
-- ====================================================================
-- This script creates the shop tables with EXACT column names and types
-- that match what the MySQL5ShopDAO.java code expects.
--
-- Based on analysis of the actual Java source code:
-- - getPendingPurchases() expects: id, char_id, item_id, quantity, gift, gifter, added
-- - getPendingRemovals() expects: id, itemUniqueId, itemOwner, amount, removed
-- ====================================================================

-- Change this to your database name
USE `not-aion`;

-- Drop existing tables if they exist (to ensure clean slate)
DROP TABLE IF EXISTS `shop_purchases`;
DROP TABLE IF EXISTS `shop_removals`;

-- Create shop_purchases table with EXACT columns from Java code
CREATE TABLE `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT NULL,
  `added` tinyint(1) NOT NULL DEFAULT '0',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  KEY `added` (`added`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create shop_removals table with EXACT columns from Java code
CREATE TABLE `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemUniqueId` int(11) NOT NULL,
  `itemOwner` int(11) NOT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `removed` int(11) NOT NULL DEFAULT '0',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `itemUniqueId` (`itemUniqueId`),
  KEY `itemOwner` (`itemOwner`),
  KEY `removed` (`removed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- VERIFICATION QUERIES
-- ====================================================================
-- Run these to verify the tables were created correctly:

-- SHOW CREATE TABLE shop_purchases;
-- SHOW CREATE TABLE shop_removals;

-- Expected columns for shop_purchases:
-- id, char_id, item_id, quantity, gift, gifter, added, purchase_date

-- Expected columns for shop_removals:
-- id, itemUniqueId, itemOwner, amount, removed, removal_date

-- ====================================================================
-- TABLES CREATED SUCCESSFULLY!
-- ====================================================================
-- The shop tables now have the EXACT column names and types that
-- the MySQL5ShopDAO.java code expects.
--
-- Java code queries:
-- shop_purchases: "SELECT id, char_id, item_id, quantity, gift, gifter FROM shop_purchases WHERE added = 0"
-- shop_removals: "SELECT id, itemUniqueId, itemOwner, amount FROM shop_removals WHERE removed = 0"
--
-- Your CashShopManager errors should now be completely resolved!
-- ====================================================================
