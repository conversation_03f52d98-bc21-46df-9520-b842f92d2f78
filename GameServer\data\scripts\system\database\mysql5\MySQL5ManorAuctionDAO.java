/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.ManorAuctionDAO;
import gameserver.model.Race;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5ManorAuctionDAO extends ManorAuctionDAO {
    private static final Logger log = Logger.getLogger(ManorAuctionDAO.class);

    @Override
    public Collection<ManorAuctionEntry> getAllEntries() {
        List<ManorAuctionEntry> entries = new ArrayList<ManorAuctionEntry>();

        PreparedStatement ps = DB
            .prepareStatement("SELECT legion_id, race_id, might FROM manor_auctions");

        try {
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                entries.add(new ManorAuctionEntry(rs.getInt("legion_id"), Race.getRaceById(rs
                    .getInt("race_id")), rs.getInt("might")));
            }

            rs.close();
        }
        catch (Exception e) {
            log.error("Error fetching Manor Auction entries: ", e);
        }
        finally {
            DB.close(ps);
        }

        return entries;
    }

    @Override
    public Collection<ManorAuctionEntry> getEntries(Race race) {
        List<ManorAuctionEntry> entries = new ArrayList<ManorAuctionEntry>();

        PreparedStatement ps = DB
            .prepareStatement("SELECT legion_id, race_id, might FROM manor_auctions WHERE race_id = ?");

        try {
            ps.setInt(1, race.getRaceId());

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                entries.add(new ManorAuctionEntry(rs.getInt("legion_id"), Race.getRaceById(rs
                    .getInt("race_id")), rs.getInt("might")));
            }

            rs.close();
        }
        catch (Exception e) {
            log.error("Error fetching Manor Auction entries: ", e);
        }
        finally {
            DB.close(ps);
        }

        return entries;
    }

    @Override
    public List<ManorAuctionEntry> getTopEntries(Race race, int limit) {
        List<ManorAuctionEntry> entries = new ArrayList<ManorAuctionEntry>();

        PreparedStatement ps = DB
            .prepareStatement("SELECT legion_id, race_id, might FROM manor_auctions WHERE race_id = ? ORDER BY might DESC LIMIT ?");

        try {
            ps.setInt(1, race.getRaceId());
            ps.setInt(2, limit);

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                entries.add(new ManorAuctionEntry(rs.getInt("legion_id"), Race.getRaceById(rs
                    .getInt("race_id")), rs.getInt("might")));
            }

            rs.close();
        }
        catch (Exception e) {
            log.error("Error fetching Manor Auction entries: ", e);
        }
        finally {
            DB.close(ps);
        }

        return entries;
    }

    @Override
    public boolean clearAllEntries() {
        // Remove auction entries
        PreparedStatement ps = DB.prepareStatement("DELETE FROM manor_auctions");

        try {
            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error resetting Manor Auction entries: ", e);
        }
        finally {
            DB.close(ps);
        }

        // Set log flag
        ps = DB.prepareStatement("UPDATE manor_auctions_log SET flag = 1");

        try {
            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error updating Manor Auction Log flag: ", e);
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean clearEntries(Race race) {
        PreparedStatement ps = DB.prepareStatement("DELETE FROM manor_auctions WHERE race_id = ?");

        try {
            ps.setInt(1, race.getRaceId());

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error resetting Manor Auction entries: ", e);
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}
