-- ====================================================================
-- AION COMBINED DATABASE SETUP SCRIPT
-- ====================================================================
-- This script creates both LoginServer and GameServer databases
-- Run this in phpMyAdmin or MySQL command line
-- ====================================================================

-- Create LoginServer Database
CREATE DATABASE IF NOT EXISTS `aionx_ls` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `aionx_ls`;

-- ====================================================================
-- LOGINSERVER TABLES
-- ====================================================================

-- ----------------------------
-- account_data
-- ----------------------------

DROP TABLE IF EXISTS `account_data`;
CREATE TABLE `account_data` (
  `id` int(11) NOT NULL auto_increment,
  `name` varchar(45) NOT NULL,
  `password` varchar(65) NOT NULL,
  `activated` boolean NOT NULL DEFAULT TRUE, 
  `access_level` tinyint(3) NOT NULL default '0',
  `membership` tinyint(3) NOT NULL default '0',
  `last_server` tinyint(3) NOT NULL default '-1',
  `last_ip` varchar(20) default NULL,
  `ip_force` varchar(20) default NULL,
  `expire` date default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- account_time
-- ----------------------------

DROP TABLE IF EXISTS `account_time`;
CREATE TABLE `account_time` (
  `account_id` int(11) NOT NULL,
  `last_active` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  `expiration_time` timestamp NULL default NULL,
  `session_duration` int(10) default '0',
  `accumulated_online` int(10) default '0',
  `accumulated_rest` int(10) default '0',
  `penalty_end` timestamp NULL default NULL,
  PRIMARY KEY  (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- banned_ip
-- ----------------------------

DROP TABLE IF EXISTS `banned_ip`;
CREATE TABLE `banned_ip` (
  `id` int(11) NOT NULL auto_increment,
  `mask` varchar(45) NOT NULL,
  `time_end` timestamp NULL default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `mask` (`mask`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default admin account (username: admin, password: admin)
INSERT INTO `account_data` (`name`, `password`, `activated`, `access_level`) 
VALUES ('admin', 'admin', TRUE, 3);

-- ====================================================================
-- Create GameServer Database
-- ====================================================================

CREATE DATABASE IF NOT EXISTS `aionx_gs` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `aionx_gs`;

-- ====================================================================
-- GAMESERVER TABLES (Key tables only - you'll need to import the full aionx_gs.sql)
-- ====================================================================

-- ----------------------------
-- server_variables
-- ----------------------------

CREATE TABLE IF NOT EXISTS `server_variables` (
  `key` varchar(30) NOT NULL,
  `value` varchar(30) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- IMPORTANT NOTICE
-- ====================================================================
-- This script only creates the basic structure and the LoginServer tables.
-- After running this script, you still need to:
-- 1. Select the aionx_gs database in phpMyAdmin
-- 2. Import the full GameServer/sql/aionx_gs.sql file
-- 
-- The reason we don't include all GameServer tables here is that the file
-- is very large (700+ lines) and it's better to import it directly.
-- ====================================================================
