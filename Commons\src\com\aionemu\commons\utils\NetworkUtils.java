/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.aionemu.commons.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR> -Nemesiss-
 */
public class NetworkUtils {
    /**
     * check if IP address match pattern
     *
     * @param pattern *.*.*.* , ***********-255 , *
     * @param address - ***********<BR>
     *                <code>address = **********  pattern = *.*.*.*   result: true<BR>
     *                address = **********  pattern = *   result: true<BR>
     *                address = **********  pattern = **********-13   result: true<BR>
     *                address = **********  pattern = **********-125   result: false<BR></code>
     * @return true if address match pattern
     */
    public static boolean checkIPMatching(String pattern, String address) {
        if (pattern.equals("*.*.*.*") || pattern.equals("*"))
            return true;

        InetAddress patternAddress = null;
        InetAddress incomingAddress = null;
        
        boolean doManualCheck = false;
        
        try
		{
			patternAddress = Inet4Address.getByName(pattern);
			incomingAddress = Inet4Address.getByName(address);
		}
		catch(UnknownHostException e)
		{
			doManualCheck = true;
		}
        
        if(!doManualCheck && patternAddress.equals(incomingAddress)){
        	return true;
        } else {
        	doManualCheck = true;
        }
        
        if(doManualCheck){
	        String[] mask = pattern.split("\\.");
	        String[] ip_address = address.split("\\.");
	        for (int i = 0; i < mask.length; i++) {
	            if (mask[i].equals("*") || mask[i].equals(ip_address[i]))
	                continue;
	            else if (mask[i].contains("-")) {
	                byte min = Byte.parseByte(mask[i].split("-")[0]);
	                byte max = Byte.parseByte(mask[i].split("-")[1]);
	                byte ip = Byte.parseByte(ip_address[i]);
	                if (ip < min || ip > max)
	                    return false;
	            } else
	                return false;
	        }
        }
        
        return true;
    }
}
