<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    <xs:element name="animations_full">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="animation" type="AnimationTemplate" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!--<xs:complexType name="AnimationTemplate">
		<xs:sequence>
			<xs:element name="times" type="TimesTemplate" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" use="required"/>
	</xs:complexType>
	<xs:complexType name="TimesTemplate">
		<xs:attribute name="race" type="xs:string"/>
		<xs:attribute name="gender" type="xs:string"/>
		<xs:attribute name="time" type="xs:string"/>
	</xs:complexType>-->
</xs:schema>