/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import loginserver.dao.AccountDAO;
import loginserver.model.Account;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;
import com.aionemu.commons.database.IUStH;

/**
 * MySQL5 Account DAO implementation
 * 
 * <AUTHOR>
 */
public class MySQL5AccountDAO extends AccountDAO{
	/**
	 * Logger
	 */
	private static final Logger	log	= Logger.getLogger(MySQL5AccountDAO.class);

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Account getAccount(String name) {
		Account account = null;
		Connection con = null;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con.prepareStatement("SELECT * FROM account_data WHERE `name` LIKE ?");

			st.setString(1, name);

			ResultSet rs = st.executeQuery();

			if(rs.next()) {
				account = new Account();

				account.setId(rs.getInt("id"));
				account.setName(name);
				account.setPasswordHash(rs.getString("password"));
				account.setAccessLevel(rs.getByte("access_level"));
				account.setMembership(rs.getByte("membership"));
				account.setActivated(rs.getByte("activated"));
				account.setLastServer(rs.getByte("last_server"));
				account.setLastIp(rs.getString("last_ip"));
				account.setIpForce(rs.getString("ip_force"));
				account.setExpire(rs.getDate("expire"));
				account.setPin(rs.getInt("pin"));
			}
		}
		catch(Exception e) {
			log.error("Can't select account with name: " + name, e);
		}
		finally {
			DatabaseFactory.close(con);
		}

		return account;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public int getAccountId(String name) {
		int id = -1;
		Connection con = null;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con.prepareStatement("SELECT `id` FROM account_data WHERE `name` LIKE ?");
			st.setString(1, name);

			ResultSet rs = st.executeQuery();

			rs.next();

			id = rs.getInt("id");
		}
		catch(SQLException e) {
			log.error("Can't select id after account insertion", e);
		}
		finally {
			DatabaseFactory.close(con);
		}

		return id;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public int getAccountCount() {
		Connection con = null;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con.prepareStatement("SELECT count(*) AS c FROM account_data");
			ResultSet rs = st.executeQuery();

			rs.next();

			return rs.getInt("c");
		}
		catch(SQLException e) {
			log.error("Can't get account count", e);
		}
		finally {
			DatabaseFactory.close(con);
		}

		return -1;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean insertAccount(Account account) {
		Connection con = null;
		int result = 0;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con
				.prepareStatement("INSERT INTO account_data(`name`, `password`, access_level, membership, activated, last_server, last_ip, ip_force, expire) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

			st.setString(1, account.getName());
			st.setString(2, account.getPasswordHash());
			st.setByte(3, account.getAccessLevel());
			st.setByte(4, account.getMembership());
			st.setByte(5, account.getActivated());
			st.setByte(6, account.getLastServer());
			st.setString(7, account.getLastIp());
			st.setString(8, account.getIpForce());
			st.setDate(9, account.getExpire());

			result = st.executeUpdate();
		}
		catch(SQLException e) {
			log.error("Can't inser account", e);
		}
		finally {
			DatabaseFactory.close(con);
		}

		if(result > 0) {
			account.setId(getAccountId(account.getName()));
		}

		return result > 0;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean updateAccount(Account account) {
		Connection con = null;
		int result = 0;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con
				.prepareStatement("UPDATE account_data SET `name` = ?, `password` = ?, access_level = ?, membership = ?, last_server = ?, last_ip = ?, ip_force = ?, expire = ? WHERE `id` = ?");

			st.setString(1, account.getName());
			st.setString(2, account.getPasswordHash());
			st.setByte(3, account.getAccessLevel());
			st.setByte(4, account.getMembership());
			st.setByte(5, account.getLastServer());
			st.setString(6, account.getLastIp());
			st.setString(7, account.getIpForce());
			st.setDate(8, account.getExpire());
			st.setInt(9, account.getId());
			st.executeUpdate();
		}
		catch(SQLException e) {
			log.error("Can't update account");
		}
		finally {
			DatabaseFactory.close(con);
		}

		return result > 0;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean updateLastServer(final int accountId, final byte lastServer) {
		return DB.insertUpdate("UPDATE account_data SET last_server = ? WHERE id = ?", new IUStH(){
			@Override
			public void handleInsertUpdate(PreparedStatement preparedStatement) throws SQLException {
				preparedStatement.setByte(1, lastServer);
				preparedStatement.setInt(2, accountId);
				preparedStatement.execute();
			}
		});
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean updateLastIp(final int accountId, final String ip) {
		return DB.insertUpdate("UPDATE account_data SET last_ip = ? WHERE id = ?", new IUStH(){
			@Override
			public void handleInsertUpdate(PreparedStatement preparedStatement) throws SQLException {
				preparedStatement.setString(1, ip);
				preparedStatement.setInt(2, accountId);
				preparedStatement.execute();
			}
		});
	}

	public boolean updateMembership(final int accountId) {
		return DB.insertUpdate("UPDATE account_data SET membership = 0, expire = NULL WHERE id = ? and expire < CURRENT_TIMESTAMP", new IUStH(){
			@Override
			public void handleInsertUpdate(PreparedStatement preparedStatement) throws SQLException {
				preparedStatement.setInt(1, accountId);
				preparedStatement.execute();
			}
		});
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String getLastIp(final int accountId) {
		String lastIp = "";
		PreparedStatement st = DB.prepareStatement("SELECT `last_ip` FROM `account_data` WHERE `id` = ?");

		try {
			st.setInt(1, accountId);
			ResultSet rs = st.executeQuery();
			if(rs.next()) {
				lastIp = rs.getString("last_ip");
			}
		}
		catch(Exception e) {
			log.error("Can't select last IP of account ID: " + accountId, e);
			return "";
		}
		finally {
			DB.close(st);
		}

		return lastIp;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean supports(String database, int majorVersion, int minorVersion) {
		return MySQL5DAOUtils.supports(database, majorVersion, minorVersion);
	}
}
