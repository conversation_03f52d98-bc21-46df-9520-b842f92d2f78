/*
 * This file is part of Aion X EMU <aionxemu.com>.
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.aionengine.chatserver.service;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Map;
import java.util.Random;

import javolution.util.FastMap;

import org.apache.log4j.Logger;

import com.aionengine.chatserver.model.ChannelType;
import com.aionengine.chatserver.model.ChatClient;
import com.aionengine.chatserver.model.channel.Channel;
import com.aionengine.chatserver.model.channel.Channels;
import com.aionengine.chatserver.model.channel.GenericChannel;
import com.aionengine.chatserver.network.aion.serverpackets.SM_PLAYER_AUTH_RESPONSE;
import com.aionengine.chatserver.network.netty.handler.ClientChannelHandler;
import com.aionengine.chatserver.network.netty.handler.ClientChannelHandler.State;

/**
 * <AUTHOR>
 */
public class ChatService
{
	private static final Logger			log			= Logger.getLogger(ChatService.class);

	private Map<Integer, ChatClient>	players		= new FastMap<Integer, ChatClient>();
	private Map<String, GenericChannel>	channels	= new FastMap<String, GenericChannel>();

	public static final ChatService getInstance()
	{
		return SingletonHolder.instance;
	}

	/**
	 * Player registered from server side
	 * 
	 * @param playerId
	 * @param token
	 * @param identifier
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public ChatClient registerPlayer(int playerId, String playerLogin, String playerName)
		throws NoSuchAlgorithmException, UnsupportedEncodingException
	{
		MessageDigest md = MessageDigest.getInstance("SHA-256");
		md.reset();
		md.update(playerLogin.getBytes("UTF-8"), 0, playerLogin.length());
		byte[] accountToken = md.digest();
		byte[] token = generateToken(accountToken);
		ChatClient chatClient = new ChatClient(playerId, token);
		chatClient.setPlayerName(playerName);
		players.put(playerId, chatClient);
		return chatClient;
	}

	public ChatClient registerPlayer(int playerId, String playerLogin) throws NoSuchAlgorithmException,
		UnsupportedEncodingException
	{
		return registerPlayer(playerId, playerLogin, "");
	}

	/**
	 * 
	 * @param playerId
	 * @return
	 */
	private byte[] generateToken(byte[] accountToken)
	{
		byte[] dynamicToken = new byte[32];
		new Random().nextBytes(dynamicToken);
		byte[] token = new byte[64];
		for(int i = 0; i < token.length; i++)
		{
			if(i < 32)
				token[i] = dynamicToken[i];
			else
				token[i] = accountToken[i - 32];
		}
		return token;
	}

	/**
	 * Player registered from client request
	 * 
	 * @param playerId
	 * @param token
	 * @param identifier
	 * @param clientChannelHandler
	 * @throws UnsupportedEncodingException 
	 */
	public void registerPlayerConnection(int playerId, byte[] token, byte[] identifier,
		ClientChannelHandler channelHandler, boolean notAion, String accountName) throws UnsupportedEncodingException
	{
		ChatClient chatClient = players.get(playerId);
		if(chatClient == null && notAion)
		{
			try
			{
				chatClient = registerPlayer(playerId, accountName);
			}
			catch(Exception e)
			{
				return;
			}
		}

		if(chatClient != null)
		{
			byte[] regToken = chatClient.getToken();
			if(Arrays.equals(regToken, token) || notAion)
			{
				if(chatClient.getPlayerName() != "")
					chatClient.setIdentifier(chatClient.getPlayerName()
						.concat("@" + GameServerService.GAMESERVER_ID).getBytes("UTF-16LE"));
				else
					chatClient.setIdentifier(identifier);

				chatClient.setChannelHandler(channelHandler);
				channelHandler.sendPacket(new SM_PLAYER_AUTH_RESPONSE());
				channelHandler.setState(State.AUTHED);
				channelHandler.setChatClient(chatClient);
				channelHandler.setAllowFlood(notAion);
				BroadcastService.getInstance().addClient(chatClient);
			}
		}
	}

	/**
	 * 
	 * @param chatClient
	 * @param channelIndex
	 * @param channelIdentifier
	 * @return
	 */
	public Channel registerPlayerWithChannel(ChatClient chatClient, int channelIndex, byte[] channelIdentifier)
	{
		Channel channel = Channels.getChannelByIdentifier(channelIdentifier);

		if(channel == null)
		{
			for(GenericChannel chan : channels.values())
			{
				if(Arrays.equals(chan.getChannelIdentifier(), channelIdentifier))
				{
					channel = chan;
					break;
				}
			}

			if(channel == null)
			{
				String chanIdentifier = new String();

				try
				{
					chanIdentifier = new String(channelIdentifier, "UTF-16le");
				}
				catch(UnsupportedEncodingException e)
				{
					e.printStackTrace();
				}

				if(chanIdentifier.contains("public"))
					channel = new GenericChannel(ChannelType.PUBLIC, channelIdentifier);
				else if(chanIdentifier.contains("trade"))
					channel = new GenericChannel(ChannelType.TRADE, channelIdentifier);
				else if(chanIdentifier.contains("User"))
					channel = new GenericChannel(ChannelType.USER, channelIdentifier);
				else if(chanIdentifier.contains("job"))
					channel = new GenericChannel(ChannelType.JOB, channelIdentifier);

				if(channel != null && !channels.containsKey(chanIdentifier))
					channels.put(chanIdentifier, (GenericChannel) channel);
			}
		}

		if(channel != null)
			chatClient.addChannel(channel);

		return channel;
	}

	/**
	 * 
	 * @param playerId
	 */
	public void playerLogout(int playerId)
	{
		ChatClient chatClient = players.get(playerId);
		if(chatClient != null)
		{
			players.remove(playerId);
			BroadcastService.getInstance().removeClient(chatClient);
			if(chatClient.getChannelHandler() != null)
				chatClient.getChannelHandler().close();
			else
				log.warn("Received logout event without client authentication for player " + playerId);
		}
	}

	/**
	 * 
	 * @param playerId
	 * @param gag
	 */
	public void gagPlayer(int playerId, boolean gag)
	{
		ChatClient chatClient = players.get(playerId);
		if(chatClient != null)
		{
			chatClient.setGagged(gag);
		}
	}

	public Channel getChannelById(int channelId)
	{
		for(GenericChannel channel : channels.values())
		{
			if(channel.getChannelId() == channelId)
				return channel;
		}
		return null;
	}

	@SuppressWarnings("synthetic-access")
	private static class SingletonHolder
	{
		protected static final ChatService	instance	= new ChatService();
	}
}
