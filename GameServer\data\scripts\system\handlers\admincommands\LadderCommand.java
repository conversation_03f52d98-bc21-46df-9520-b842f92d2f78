/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.dao.LadderDAO;
import gameserver.dao.MightDAO;
import gameserver.dao.PlayerDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.PlayerCommonData;
import gameserver.model.templates.item.ItemTemplate;
import gameserver.services.MailService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.utils.idfactory.IDFactory;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class LadderCommand extends AdminCommand {
    public LadderCommand() {
        super("ladder");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < 5) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length == 0) {
            showSyntax(admin);
            return;
        }

        if ("reward".startsWith(params[0].toLowerCase())) {
            if (params.length < 5) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //ladder reward <top> <bottom> <itemId> <amount>"
                        + "\nSyntax: //ladder reward <top> <bottom> might <amount>");
                return;
            }

            int top = Integer.parseInt(params[1]);
            int bottom = Integer.parseInt(params[2]);
            int amount = Integer.parseInt(params[4]);

            DAOManager.getDAO(LadderDAO.class).updateRanks();

            List<String> list = DAOManager.getDAO(LadderDAO.class).getPlayerTopList(top, bottom);

            if (params[3].equalsIgnoreCase("might")) {
                for (String name : list) {
                    int accountId = DAOManager.getDAO(PlayerDAO.class).getAccountIdByName(
                        Util.convertName(name));

                    if (DAOManager.getDAO(MightDAO.class).addMight(accountId, amount))
                        PacketSendUtility.sendMessage(admin, "[+] Added " + amount + " Might to "
                            + name);
                    else
                        PacketSendUtility.sendMessage(admin, "[-] Failed to add " + amount
                            + " Might to " + name);
                }
            }
            else {
                int itemId = Integer.parseInt(params[3]);

                ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);

                if (template == null) {
                    PacketSendUtility.sendMessage(admin, "Error! Please enter a valid itemId.");
                    return;
                }

                for (String name : list) {
                    PlayerCommonData recipient = DAOManager.getDAO(PlayerDAO.class)
                        .loadPlayerCommonDataByName(Util.convertName(name));

                    Item attachedItem = new Item(IDFactory.getInstance().nextItemId(), itemId,
                        template, amount, null, false, 0);

                    if (template.isWeapon() || template.isArmor()) {
                        if (template.getRandomOption() != 0)
                            attachedItem.setOptionalSocket(-1);
                        else {
                            attachedItem.setOptionalSocket(template.getOptionSlotBonus());
                            attachedItem.setBonusEnchant(template.getMaxEnchantBonus());
                        }
                    }

                    if (MailService.getInstance().sendSystemMail(
                        "Ladder",
                        "Ladder Reward",
                        "Congratulations! Here's a reward for your placement in Top " + bottom
                            + " on the BG ladder.", recipient.getPlayerObjId(), attachedItem, 0))
                        PacketSendUtility.sendMessage(admin, "[+] Sent reward mail to " + name
                            + " successfully.");
                    else
                        PacketSendUtility.sendMessage(admin, "[-] Failed sending reward mail to "
                            + name + "!");
                }
            }
        }
        else if ("refresh".startsWith(params[0].toLowerCase())) {
            DAOManager.getDAO(LadderDAO.class).updateRanks();

            PacketSendUtility.sendMessage(admin, "Ladder ranks have been updated.");
        }
        else {
            showSyntax(admin);
        }
    }

    private void showSyntax(Player player) {
        PacketSendUtility.sendMessage(player, "Syntax: //ladder <reward | refresh>");
    }
}
