-- ====================================================================
-- EMERGENCY SHOP TABLES FIX
-- ====================================================================
-- This script will fix the shop table issues once and for all.
-- Copy and paste this ENTIRE script into phpMyAdmin SQL tab and run it.
-- ====================================================================

-- STEP 1: Make sure we're using the right database
-- Change 'not-aion' to your actual database name if different
USE `not-aion`;

-- STEP 2: Drop any existing shop tables (clean slate)
DROP TABLE IF EXISTS `shop_purchases`;
DROP TABLE IF EXISTS `shop_removals`;

-- STEP 3: Create shop_purchases with EXACT columns the Java code expects
CREATE TABLE `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT NULL,
  `added` tinyint(1) NOT NULL DEFAULT '0',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  KEY `added` (`added`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- STEP 4: Create shop_removals with EXACT columns the Java code expects
CREATE TABLE `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemUniqueId` int(11) NOT NULL,
  `itemOwner` int(11) NOT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `removed` int(11) NOT NULL DEFAULT '0',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `itemUniqueId` (`itemUniqueId`),
  KEY `itemOwner` (`itemOwner`),
  KEY `removed` (`removed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- STEP 5: Verify tables were created correctly
SELECT 'shop_purchases table created successfully' AS status;
DESCRIBE shop_purchases;

SELECT 'shop_removals table created successfully' AS status;
DESCRIBE shop_removals;

-- ====================================================================
-- INSTRUCTIONS:
-- ====================================================================
-- 1. Copy this ENTIRE script
-- 2. Go to phpMyAdmin
-- 3. Select your database (probably 'not-aion')
-- 4. Click "SQL" tab
-- 5. Paste this script
-- 6. Click "Go"
-- 7. Restart your GameServer
-- 
-- The errors should be completely gone!
-- ====================================================================
