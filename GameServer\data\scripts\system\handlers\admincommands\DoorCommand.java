/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.StaticDoor;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.StaticDoorTemplate.DoorState;
import gameserver.services.TeleportService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class DoorCommand extends AdminCommand {

    public DoorCommand() {
        super("door");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //door <listnear | listmap | open | close | goto>");
            return;
        }

        if ("listnear".equalsIgnoreCase(params[0])) {
            StringBuilder sb = new StringBuilder();

            sb.append("### LISTING NEARBY DOORS ###\n");

            for (AionObject ao : admin.getKnownList().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                StaticDoor door = (StaticDoor) ao;

                sb.append("- ID: ");
                sb.append(door.getDoorId());
                sb.append(" at distance ");
                sb.append(String.format("%.02f m", MathUtil.getDistance(admin, door)));
                sb.append(" is ");
                sb.append(DoorState.isSet(DoorState.OPEN, door.getState()) ? "OPEN" : "CLOSED");
                sb.append("\n");
            }
            
            sb.append("### END OF DOORS ###");
            PacketSendUtility.sendMessage(admin, sb.toString());
        }
        else if ("listmap".equalsIgnoreCase(params[0])) {
            StringBuilder sb = new StringBuilder();

            sb.append("### LISTING DOORS ON MAP ###\n");

            for (AionObject ao : admin.getWorldMapInstance().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                StaticDoor door = (StaticDoor) ao;

                sb.append("- ID: ");
                sb.append(door.getDoorId());
                sb.append(" at distance ");
                sb.append(String.format("%.02f m", MathUtil.getDistance(admin, door)));
                sb.append(" is ");
                sb.append(DoorState.isSet(DoorState.OPEN, door.getState()) ? "OPEN" : "CLOSED");
                sb.append("\n");
            }
            
            sb.append("### END OF DOORS ###");
            PacketSendUtility.sendMessage(admin, sb.toString());
        }
        else if ("open".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //door open <doorId>");
                return;
            }

            int doorId = Integer.parseInt(params[1]);

            StaticDoor door = null;

            for (AionObject ao : admin.getWorldMapInstance().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                    door = (StaticDoor) ao;
                    break;
                }
            }

            if (door != null) {
                door.setOpen(true);
                PacketSendUtility.sendMessage(admin, "Door " + doorId + " has been opened.");
            }
            else {
                PacketSendUtility
                    .sendMessage(admin, "Error! No door with the specified id on map.");
            }
        }
        else if ("close".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //door close <doorId>");
                return;
            }

            int doorId = Integer.parseInt(params[1]);

            StaticDoor door = null;

            for (AionObject ao : admin.getWorldMapInstance().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                    door = (StaticDoor) ao;
                    break;
                }
            }

            if (door != null) {
                door.setOpen(false);
                PacketSendUtility.sendMessage(admin, "Door " + doorId + " has been closed.");
            }
            else {
                PacketSendUtility
                    .sendMessage(admin, "Error! No door with the specified id on map.");
            }
        }
        else if ("goto".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //door goto <doorId>");
                return;
            }

            int doorId = Integer.parseInt(params[1]);

            StaticDoor door = null;

            for (AionObject ao : admin.getWorldMapInstance().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                    door = (StaticDoor) ao;
                    break;
                }
            }

            if (door != null) {
                TeleportService.teleportTo(admin, door.getWorldId(), door.getX(), door.getY(),
                    door.getZ() + 1f, 0);
                PacketSendUtility.sendMessage(admin, "You have teleported to door " + doorId + ".");
            }
            else {
                PacketSendUtility
                    .sendMessage(admin, "Error! No door with the specified id on map.");
            }
        }
    }
}
