/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class Reconnect extends UserCommand {
    public Reconnect() {
        super("reconnect");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.getBattleground() != null)
            return;

        gameserver.model.pvpevents.Battleground bg = LadderService.getInstance()
            .getActiveBattleground(player);
        if (bg == null) {
            message(player, "You have no active battleground to reconnect to.");
            return;
        }
        else {
            bg.reconnectPlayer(player);
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }
}