# Aion Database Setup Guide for XAMPP/MariaDB

## Problem
The error `Key column 'account_id' doesn't exist in table` occurs because the GameServer database references account information that should exist in the LoginServer database, but you're trying to import only the GameServer database.

## Solution
You need to set up **TWO separate databases** in the correct order:

### Step 1: Create Databases in phpMyAdmin

1. Open XAMPP Control Panel and start Apache and MySQL
2. Go to `http://localhost/phpmyadmin`
3. Create two new databases:
   - `aionx_ls` (for LoginServer)
   - `aionx_gs` (for GameServer)

### Step 2: Import LoginServer Database First

1. Select the `aionx_ls` database
2. Go to "Import" tab
3. Choose file: `LoginServer/sql/aionx_ls.sql`
4. Click "Go" to import

### Step 3: Import GameServer Database Second

1. Select the `aionx_gs` database  
2. Go to "Import" tab
3. Choose file: `GameServer/sql/aionx_gs.sql`
4. Click "Go" to import

## Database Structure

### LoginServer Database (`aionx_ls`)
- `account_data` - User accounts with login credentials
- `account_time` - Account session and time tracking
- `banned_ip` - IP ban management

### GameServer Database (`aionx_gs`)
- `players` - Player characters (references `account_data.id` via `account_id`)
- `player_appearance` - Character appearance data
- `player_macrosses` - Player macros
- `player_titles` - Player titles
- And many more game-related tables...

## Configuration Files to Update

After setting up the databases, you'll need to update the configuration files:

### LoginServer Configuration
File: `LoginServer/config/database.properties`
```properties
database.driver=com.mysql.jdbc.Driver
database.url=****************************************************************************
database.user=root
database.password=
```

### GameServer Configuration  
File: `GameServer/config/database.properties`
```properties
database.driver=com.mysql.jdbc.Driver
database.url=****************************************************************************
database.user=root
database.password=
```

## Troubleshooting

If you still get errors:

1. **Check MySQL/MariaDB is running** in XAMPP
2. **Verify database names** match exactly in config files
3. **Check MySQL user permissions** (default XAMPP uses root with no password)
4. **Import in correct order** - LoginServer first, then GameServer
5. **Check for foreign key constraints** - some tables reference others

## Next Steps

After successful database import:
1. Test the LoginServer startup
2. Test the GameServer startup  
3. Create a test account in the `account_data` table
4. Test character creation and login
