/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.PlayerDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.PlayerCommonData;
import gameserver.model.templates.item.ItemTemplate;
import gameserver.services.MailService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.utils.idfactory.IDFactory;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
public class MailCommand extends AdminCommand {
    public MailCommand() {
        super("mail");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ADD) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 5) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //mail <recipient> <sender> <title> <message> <itemid> [quantity]"
                        + "\nSyntax: //mail <sender> <title> <message> <itemid> <quantity> {recipient1} {recipient2} {...}");
            return;
        }

        if (params.length > 6) {
            int itemId = Integer.parseInt(params[3]);
            long quantity = Long.parseLong(params[4]);

            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);
            if (template == null) {
                PacketSendUtility.sendMessage(admin, "No item with that id exists.");
                return;
            }

            for (int i = 5; i < params.length; i++) {
                PlayerCommonData recipient = DAOManager.getDAO(PlayerDAO.class)
                    .loadPlayerCommonDataByName(Util.convertName(params[i]));
                if (recipient == null) {
                    PacketSendUtility.sendMessage(admin, "No player with the name " + params[0]
                        + " was found!");
                    continue;
                }

                Item attachedItem = new Item(IDFactory.getInstance().nextItemId(), itemId,
                    template, quantity, null, false, 0);

                if (template.isWeapon() || template.isArmor()) {
                    if (template.getRandomOption() != 0)
                        attachedItem.setOptionalSocket(-1);
                    else {
                        attachedItem.setOptionalSocket(template.getOptionSlotBonus());
                        attachedItem.setBonusEnchant(template.getMaxEnchantBonus());
                    }
                }

                if (MailService.getInstance().sendSystemMail(params[0], params[1], params[2],
                    recipient.getPlayerObjId(), attachedItem, 0)) {
                    PacketSendUtility.sendMessage(admin, "You have successfully sent a mail to "
                        + recipient.getName());
                }
                else {
                    PacketSendUtility.sendMessage(admin, "Error occured when sending mail to "
                        + recipient.getName());
                }
            }
        }
        else {
            PlayerCommonData recipient = DAOManager.getDAO(PlayerDAO.class)
                .loadPlayerCommonDataByName(Util.convertName(params[0]));
            if (recipient == null) {
                PacketSendUtility.sendMessage(admin, "No player with that name was found!");
                return;
            }

            int itemId = Integer.parseInt(params[4]);
            long quantity = 1;

            if (params.length >= 6)
                quantity = Long.parseLong(params[5]);

            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);
            if (template == null) {
                PacketSendUtility.sendMessage(admin, "No item with that id exists.");
                return;
            }

            Item attachedItem = new Item(IDFactory.getInstance().nextItemId(), itemId, template,
                quantity, null, false, 0);

            if (template.isWeapon() || template.isArmor()) {
                if (template.getRandomOption() != 0)
                    attachedItem.setOptionalSocket(-1);
                else {
                    attachedItem.setOptionalSocket(template.getOptionSlotBonus());
                    attachedItem.setBonusEnchant(template.getMaxEnchantBonus());
                }
            }

            if (MailService.getInstance().sendSystemMail(params[1], params[2], params[3],
                recipient.getPlayerObjId(), attachedItem, 0)) {
                PacketSendUtility.sendMessage(admin, "The mail has been successfully sent!");
            }
            else {
                PacketSendUtility.sendMessage(admin, "An error occured while sending the mail!");
            }
        }
    }
}
