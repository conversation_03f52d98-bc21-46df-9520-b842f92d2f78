/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.CommanderChatService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * <AUTHOR>
 * 
 */
public class Commander extends AdminCommand {

    public Commander() {
        super("commander");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //commander <add | remove | list | clear>");
            return;
        }

        if ("list".startsWith(params[0])) {
            PacketSendUtility.sendMessage(admin, CommanderChatService.getInstance()
                .getCommanderList());
        }
        else if ("add".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //commander add <name>");
                return;
            }

            Player player = World.getInstance().findPlayer(params[1]);
            if (player == null) {
                PacketSendUtility.sendMessage(admin, "The player " + params[1] + " is not online.");
                return;
            }

            if (CommanderChatService.getInstance().isCommander(player)) {
                PacketSendUtility.sendMessage(admin, "The player " + player.getName()
                    + " is already a Commander.");
                return;
            }

            CommanderChatService.getInstance().addCommander(player);

            PacketSendUtility.sendMessage(admin, player.getName()
                + " has been granted .commander chat access");
            PacketSendUtility.sendMessage(player,
                "You have been granted access to the .commander chat");
        }
        else if ("remove".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility
                    .sendMessage(admin, "Syntax: //commander remove <name | objectId>");
                return;
            }

            int objectId = 0;

            try {
                objectId = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
            }

            if (objectId == 0) {
                Player player = World.getInstance().findPlayer(params[1]);
                if (player == null) {
                    PacketSendUtility.sendMessage(admin, "The player " + params[1]
                        + " is not online.");
                    return;
                }

                if (!CommanderChatService.getInstance().isCommander(player)) {
                    PacketSendUtility.sendMessage(admin, "The player " + player.getName()
                        + " is not a Commander.");
                    return;
                }

                CommanderChatService.getInstance().removeCommander(player);

                PacketSendUtility.sendMessage(admin, player.getName()
                    + " has lost .commander chat access");
                PacketSendUtility.sendMessage(player,
                    "You have lost your access to the .commander chat");
            }
            else {
                if (!CommanderChatService.getInstance().isCommander(objectId)) {
                    PacketSendUtility.sendMessage(admin, "The player with id " + objectId
                        + " is not a Commander.");
                    return;
                }

                CommanderChatService.getInstance().removeCommander(objectId);

                PacketSendUtility.sendMessage(admin, "Player " + objectId
                    + " has lost .commander chat access");
            }
        }
        else if ("clear".startsWith(params[0])) {
            for (Integer objectId : CommanderChatService.getInstance().getCommanders()) {
                Player player = World.getInstance().findPlayer(objectId);

                if (player != null) {
                    PacketSendUtility.sendMessage(player,
                        "You have lost your access to the .commander chat");
                }
            }

            CommanderChatService.getInstance().clearCommanders();

            PacketSendUtility.sendMessage(admin, "Cleared the list of commanders!");
        }
        else {
            PacketSendUtility.sendMessage(admin, "Invalid option specified!");
        }
    }
}
