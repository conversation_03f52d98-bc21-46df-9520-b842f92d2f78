package usercommands;

import gameserver.dao.MightDAO;
import gameserver.dao.ShopDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.listeners.ItemEquipmentListener;
import gameserver.model.templates.RandomGroup;
import gameserver.model.templates.RandomOptionTemplate;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.services.ArenaService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

public class TuneCommand extends UserCommand {
    private static final int TUNE_COST = 5;
    private static final int BEST_TUNE_COST = 75;

    public TuneCommand() {
        super("tune");
    }

    public void executeCommand(Player player, String param) {
        String[] params = Util.splitCommandArgs(param);

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)
            || player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player,
                "You cannot tune items while in BG, FFA, 1v1 or simply combat.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(player,
                "Syntax: .tune <itemid> -- re-tunes the item for " + TUNE_COST + " Might."
                    + "\nOR Syntax: .tune best <itemid> -- tunes the item to the best version for "
                    + BEST_TUNE_COST + " Might.");
            return;
        }
        else {
            boolean best = params.length >= 2 && "best".equalsIgnoreCase(params[0]);

            if (best) {
                if (DAOManager.getDAO(MightDAO.class).getMight(player) < BEST_TUNE_COST) {
                    PacketSendUtility.sendMessage(player, "You need to have " + BEST_TUNE_COST
                        + " Might to use this command with option best.");
                    return;
                }
            }
            else {
                if (DAOManager.getDAO(MightDAO.class).getMight(player) < TUNE_COST) {
                    PacketSendUtility.sendMessage(player, "You need to have " + TUNE_COST
                        + " Might to use this command.");
                    return;
                }
            }

            int itemId = 0;
            try {
                if (params[0].startsWith("[item: "))
                    itemId = Integer.parseInt(params[best ? 1 : 0].substring(7, 16));
                else if (params[0].startsWith("[item:"))
                    itemId = Integer.parseInt(params[best ? 1 : 0].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[best ? 1 : 0]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Item id's are numbers like 100100715 or [item:100100715]!");
                return;
            }

            List<Item> items = player.getInventory().getAllItemsByItemId(itemId);
            if (items.isEmpty()) {
                PacketSendUtility.sendMessage(player, "You do not have [item:" + itemId
                    + "] in your inventory!");
                return;
            }

            Item item = items.get(0);
            if (item.getItemTemplate().getRandomOption() == 0) {
                PacketSendUtility.sendMessage(player, "[item:" + itemId
                    + "] is not a re-tuneable item!");
                return;
            }

            if (best) {
                DAOManager.getDAO(MightDAO.class).addMight(player, -BEST_TUNE_COST);
                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -7, itemId, BEST_TUNE_COST);

                RandomOptionTemplate template = DataManager.RANDOM_OPTION_DATA
                    .getRandomOptionTemplate(item.getItemTemplate().getRandomOption());

                for (RandomGroup random : template.getGroups()) {
                    item.setRandomOption(random.getId());
                    break;
                }

                PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(item));

                if (item.isEquipped())
                    ItemEquipmentListener.addRandomOptionEffect(player, item);

                PacketSendUtility.sendMessage(player, "[item:" + itemId
                    + "] has been re-tuned to the best option for " + BEST_TUNE_COST + " Might.");
            }
            else {
                DAOManager.getDAO(MightDAO.class).addMight(player, -TUNE_COST);
                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -7, itemId, TUNE_COST);

                item.tune();

                PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(item));

                if (item.isEquipped())
                    ItemEquipmentListener.addRandomOptionEffect(player, item);

                PacketSendUtility.sendMessage(player, "[item:" + itemId
                    + "] has been re-tuned for " + TUNE_COST + " Might.");
            }
        }
    }
}