/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ItemRemodelService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * 
 * <AUTHOR>
 */
public class Remodel extends AdminCommand {
    public Remodel() {
        super("remodel");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_DYE) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //remodel <itemid> OR\n"
                + "Syntax: //remodel <name> <itemid>");
            return;
        }

        if (params[0].startsWith("[item:") || params.length == 1) { // Use self
            int itemId = 0;
            try {
                if (params[0].startsWith("[item: "))
                    itemId = Integer.parseInt(params[0].substring(7, 16));
                else if (params[0].startsWith("[item:"))
                    itemId = Integer.parseInt(params[0].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[0]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(admin,
                    "Error! Item id's are numbers like ********* or [item:*********]!");
                return;
            }

            if (ItemRemodelService.commandRemodelItem(admin, itemId)) {
                PacketSendUtility
                    .sendMessage(admin, "Successfully remodelled an item of yourself!");
            }
            else {
                PacketSendUtility
                    .sendMessage(admin, "Was not able to remodel an item of yourself!");
            }
        }
        else if (params.length == 2) { // Use name
            String name = params[0];

            int itemId = 0;
            try {
                if (params[1].startsWith("[item: "))
                    itemId = Integer.parseInt(params[1].substring(7, 16));
                else if (params[1].startsWith("[item:"))
                    itemId = Integer.parseInt(params[1].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(admin,
                    "Error! Item id's are numbers like ********* or [item:*********]!");
                return;
            }

            VisibleObject target = World.getInstance().findPlayer(Util.convertName(name));
            if (target == null) {
                PacketSendUtility.sendMessage(admin, "The player is not online!");
                return;
            }

            if (ItemRemodelService.commandRemodelItem((Player) target, itemId)) {
                PacketSendUtility.sendMessage(admin,
                    "Successfully remodelled an item of the player!");
            }
            else {
                PacketSendUtility.sendMessage(admin,
                    "Was not able to remodel an item of the player!");
            }
        }
    }
}