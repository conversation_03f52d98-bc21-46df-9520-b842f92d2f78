/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StoneStatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.TreeSet;

/**
 * <AUTHOR>
 * 
 */
public class PowerUp extends AdminCommand {

    public PowerUp() {
        super("powerup");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_POWERUP) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //powerup <times> -- gives you monster HP by <times>\n"
                    + "Specify 0 to remove the bonus!");
            return;
        }

        VisibleObject vObj = admin.getTarget();
        if (!(vObj instanceof Creature)) {
            PacketSendUtility.sendMessage(admin, "Please select a creature as target!");
            return;
        }

        Creature target = (Creature) vObj;

        int times = Integer.parseInt(params[0]);

        if (times == 0) { // clear bonus
            target.getGameStats().endEffect(StoneStatEffectId.getInstance(0, 0));

            PacketSendUtility.sendMessage(admin,
                "You have ended any powerups on " + target.getName());
        }
        else {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(AddModifier.newInstance(StatEnum.MAXHP,
                target.getGameStats().getCurrentStat(StatEnum.MAXHP) * times, true));

            target.getGameStats().endEffect(StoneStatEffectId.getInstance(0, 0)); // Remove first
            target.getGameStats().addModifiers(StoneStatEffectId.getInstance(0, 0), mods); // Then apply

            PacketSendUtility.sendMessage(admin, target.getName() + " has been powered up " + times
                + " times!");
        }
    }
}
