/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import loginserver.configs.Config;
import loginserver.dao.AccessLogDAO;
import loginserver.network.aion.AionAuthResponse;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * 
 * <AUTHOR>
 */
public class MySQL5AccessLogDAO extends AccessLogDAO{
	/**
	 * Logger
	 */
	private static final Logger	log	= Logger.getLogger(MySQL5AccessLogDAO.class);

	@Override
	public void logAccess(String user, AionAuthResponse response, String ip) {
		Connection con = null;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con.prepareStatement("INSERT INTO access_log (account_name, date, response, ip) VALUES (?,NOW(),?,?)");

			st.setString(1, user);
			st.setInt(2, response.getMessageId());
			st.setString(3, ip);

			st.execute();
		}
		catch(SQLException e) {
			log.error("Failed to log access: ", e);
		}
		finally {
			DatabaseFactory.close(con);
		}
	}

	@Override
	public int getFailedLoginAttempts(String ip) {
		Connection con = null;
		int result = 0;

		try {
			con = DatabaseFactory.getConnection();
			PreparedStatement st = con
				.prepareStatement("SELECT * FROM access_log WHERE ip LIKE ? AND response = 2 AND date > DATE_SUB(NOW(), INTERVAL ? MINUTE)");
			st.setString(1, ip);
			st.setInt(2, Config.WRONG_LOGIN_BAN_TIME);

			ResultSet rset = st.executeQuery();

			if(rset.last())
				result = rset.getRow();
		}
		catch(SQLException e) {
			log.error("Failed to fetch failed login attempts: ", e);
		}
		finally {
			DatabaseFactory.close(con);
		}

		return result;
	}

	@Override
	public boolean supports(String database, int majorVersion, int minorVersion) {
		return MySQL5DAOUtils.supports(database, majorVersion, minorVersion);
	}
}
