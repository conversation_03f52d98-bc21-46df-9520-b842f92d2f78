#
# This file is part of Aion X Emu <aionxemu.com>.
#
# This is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser Public License for more details.
#
# You should have received a copy of the GNU Lesser Public License
# along with this software.  If not, see <http://www.gnu.org/licenses/>.
#
# ----------------------------
# Chat Server Configs
# ----------------------------

# <PERSON> will listen for connections on specified address
chatserver.network.client.address = localhost:10241

# Address that will be used by CS to listen for GS connections
chatserver.network.gameserver.address = localhost:9021

# Password to match for successful authentication of the game server
# NOTE: Don't forget to add your password!
# ANOTHER NOTE: Don't forget to enable chat in your Gameserver Config File!
chatserver.network.gameserver.password = aion