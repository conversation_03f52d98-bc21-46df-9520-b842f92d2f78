package com.aionengine.chatserver.network.aion.serverpackets;

import org.jboss.netty.buffer.ChannelBuffer;

import com.aionengine.chatserver.model.message.Message;
import com.aionengine.chatserver.network.aion.AbstractServerPacket;
import com.aionengine.chatserver.network.netty.handler.ClientChannelHandler;

/**
 * 
 * <AUTHOR>
 *
 */
public class SM_CHANNEL_MESSAGE extends AbstractServerPacket
{
	
	private Message message;
	
	public SM_CHANNEL_MESSAGE(Message message)
	{
		super(0x1A);
		this.message = message;
	}

	@Override
	protected void writeImpl(ClientChannel<PERSON><PERSON><PERSON> cHand<PERSON>, ChannelBuffer buf)
	{
		writeC(buf, getOpCode());
		writeC(buf, 0x00);
		writeD(buf, message.getSender().getClientId());
		writeD(buf, 0x00);
		writeD(buf, message.getChannel().getChannelId());
		writeD(buf, message.getSender().getClientId());
		writeD(buf, 0x00);
		writeC(buf, 0x00);
		writeH(buf, message.getSender().getIdentifier().length / 2);
		writeB(buf, message.getSender().getIdentifier());
		writeH(buf, message.size() / 2);
		writeB(buf, message.getText());
	}

}
