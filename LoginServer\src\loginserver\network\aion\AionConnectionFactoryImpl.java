/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package loginserver.network.aion;

import java.io.IOException;
import java.nio.channels.SocketChannel;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.locks.ReentrantLock;

import loginserver.utils.ThreadPoolManager;

import org.apache.log4j.Logger;

import com.aionemu.commons.network.AConnection;
import com.aionemu.commons.network.ConnectionFactory;
import com.aionemu.commons.network.Dispatcher;

/**
 * ConnectionFactory implementation that will be creating AionConnections
 * 
 * <AUTHOR>
 */
public class AionConnectionFactoryImpl implements ConnectionFactory{
	private final Logger	log	= Logger.getLogger(AionConnectionFactoryImpl.class);
	private FloodManager	floodMgr;

	public AionConnectionFactoryImpl() {
		floodMgr = new FloodManager();
	}

	/**
	 * Create a new {@link com.aionemu.commons.network.AConnection AConnection} instance.<br>
	 * 
	 * @param socket
	 *            that new {@link com.aionemu.commons.network.AConnection AConnection} instance will represent.<br>
	 * @param dispatcher
	 *            to witch new connection will be registered.<br>
	 * @return a new instance of {@link com.aionemu.commons.network.AConnection AConnection}<br>
	 * @throws IOException
	 * @see com.aionemu.commons.network.AConnection
	 * @see com.aionemu.commons.network.Dispatcher
	 */
	@Override
	public AConnection create(SocketChannel socket, Dispatcher dispatcher) throws IOException {
		if(floodMgr.isFlooding(socket.socket().getInetAddress().getHostAddress())) {
			log.warn("Flood detected from " + socket.socket().getInetAddress().getHostAddress() + " - rejecting connection.");
			socket.close();
			return null;
		}

		return new AionConnection(socket, dispatcher);
	}

	private class FloodManager{
		private static final int		FLOOD_FLUSH_INTERVAL	= 60 * 1000;

		private static final int		FLOOD_THRESHOLD			= 20;
		private static final int		FLOOD_INCREMENT			= 1;

		private ScheduledFuture<?>		flushTask;

		private Map<String, Integer>	floodRecords			= new ConcurrentHashMap<String, Integer>();
		private ReentrantLock			lock					= new ReentrantLock();

		public FloodManager() {
			flushTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable(){
				@Override
				public void run() {
					flush();
				}
			}, FLOOD_FLUSH_INTERVAL, FLOOD_FLUSH_INTERVAL);
		}

		private void flush() {
			floodRecords.clear();
		}

		public boolean isFlooding(String ip) {
			if(ip == null || ip.isEmpty())
				return true;

			int floodRecord = -1;

			lock.lock();

			try {
				if(floodRecords.containsKey(ip))
					floodRecords.put(ip, floodRecords.get(ip) + FLOOD_INCREMENT);
				else
					floodRecords.put(ip, FLOOD_INCREMENT);

				floodRecord = floodRecords.get(ip);
			}
			finally {
				lock.unlock();
			}

			return floodRecord == -1 ? true : floodRecord > FLOOD_THRESHOLD;
		}
	}
}
