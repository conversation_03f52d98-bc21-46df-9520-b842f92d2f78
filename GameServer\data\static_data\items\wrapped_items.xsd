<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:include schemaLocation="../bonuses/bonuses.xsd" />

  <xs:element name="wrapped_items">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="wrapper_item" type="WrapperItem"
			minOccurs="1" maxOccurs="unbounded" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:complexType name="WrapperItem">
	<xs:sequence>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" name="item" type="WrappedItem" />
      </xs:sequence>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" name="wrapper_item" type="WrapperItem" />
      </xs:sequence>
    </xs:sequence>
    <xs:attribute default="0" name="id" type="xs:int" />
    <xs:attribute default="0" name="count" type="xs:int" />
    <xs:attribute default="false" name="random" type="xs:boolean" />
	<xs:attribute use="optional" name="description" type="xs:string" />
   </xs:complexType>

  <xs:complexType name="WrappedItem">
    <xs:attribute default="0" name="id" type="xs:int" />
    <xs:attribute default="NONE" name="type" use="optional">
      <xs:simpleType>
        <xs:restriction base="bonusType" />
      </xs:simpleType>
    </xs:attribute>
    <xs:attribute name="level" type="xs:int" use="optional" />
    <xs:attribute name="min" type="xs:int" use="required" />
    <xs:attribute name="max" type="xs:int" use="required" />
    <xs:attribute name="race" type="itemRace" default="ALL" />
    <xs:attribute name="name" type="xs:string" use="optional" />
  </xs:complexType>

</xs:schema>