/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.dao.MightDAO;
import gameserver.dao.ShopDAO;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_DELETE_ITEM;
import gameserver.services.ItemRemodelService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class SkinCommand extends UserCommand {
    private static final int SKIN_REMODEL_COST = 50;

    public SkinCommand() {
        super("skin");
    }

    public void executeCommand(Player player, String param) {
        String[] params = Util.splitCommandArgs(param);

        if (params.length < 1 || params[0] == "") {
            PacketSendUtility
                .sendMessage(
                    player,
                    "Syntax: .skin <itemid> -- remodels an item for "
                        + SKIN_REMODEL_COST
                        + " Might from your inventory onto an equipped item of the same type. This allows you to use Costumes and Special Skins on unremodelable items.");
            return;
        }
        else {
            if (DAOManager.getDAO(MightDAO.class).getMight(player) < SKIN_REMODEL_COST) {
                PacketSendUtility.sendMessage(player, "You need to have " + SKIN_REMODEL_COST
                    + " Might to use this command.");
                return;
            }

            int itemId = 0;
            try {
                if (params[0].startsWith("[item: "))
                    itemId = Integer.parseInt(params[0].substring(7, 16));
                else if (params[0].startsWith("[item:"))
                    itemId = Integer.parseInt(params[0].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[0]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Item id's are numbers like 100100715 or [item:100100715]!");
                return;
            }

            List<Item> items = player.getInventory().getAllItemsByItemId(itemId);
            if (items.isEmpty()) {
                PacketSendUtility.sendMessage(player, "You do not have [item: " + itemId
                    + "] in your inventory!");
                return;
            }

            int skinId = items.get(0).getItemSkinTemplate().getTemplateId();

            if (ItemRemodelService.commandRemodelItem(player, skinId)) {
                player.getInventory().removeFromBag(items.get(0), true);
                PacketSendUtility
                    .sendPacket(player, new SM_DELETE_ITEM(items.get(0).getObjectId()));
                DAOManager.getDAO(MightDAO.class).addMight(player, -SKIN_REMODEL_COST);

                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -5, skinId, SKIN_REMODEL_COST);

                PacketSendUtility.sendMessage(player,
                    "Successfully remodeled one of your items to [item: " + skinId + "]!");
            }
            else {
                PacketSendUtility.sendMessage(player,
                    "It was not possible to remodel any of your equipped items to [item: " + itemId
                        + "]!");
            }
        }
    }
}
