/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.ArenaService;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 */
public class FfaCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    public FfaCommand() {
        super("ffa");
    }

    public void executeCommand(final Player player, String param) {
        if (player.isTemporary())
            return;

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 10000) {
                message(player, "You cannot use this command more than every 10 seconds!");
                return;
            }
        }

        if (player.getBattleground() != null || LadderService.getInstance().isInQueue(player)
            || player.isSpectating() || player.getLifeStats().isAlreadyDead()) {
            message(
                player,
                "You cannot enter the FFA map while in a battleground, in the queue, while spectating or being dead.");
            return;
        }

        if (ArenaService.getInstance().isInArena(player)) {
            message(player, "You will be leaving the FFA map in 10 seconds!");
            ArenaService.getInstance().leaveArena(player, false);
        }
        else {
            if (player.isInCombatLong()) {
                message(player, "You cannot enter the FFA map while in combat.");
                return;
            }
            else if (player.isAllFriend()) {
                message(player, "You cannot enter the FFA map from a neutral map.");
                return;
            }

            message(player,
                "You will be entering the FFA map in 10 seconds. To leave again, type .ffa!");
            ArenaService.getInstance().enterArena(player, false, false);
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }
}