/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package loginserver.network.gameserver.clientpackets;

import java.nio.ByteBuffer;

import loginserver.GameServerInfo;
import loginserver.controller.AccountController;
import loginserver.network.gameserver.GsClientPacket;
import loginserver.network.gameserver.GsConnection;

import org.apache.log4j.Logger;

/**
 * Reads the list of accoutn id's that are logged to game server
 * 
 * <AUTHOR>
 */
public class CM_CLEAR_COUNTS extends GsClientPacket{

	/**
	 * Creates new packet instance.
	 * 
	 * @param buf
	 *            packet data
	 * @param client
	 *            client
	 */
	public CM_CLEAR_COUNTS(ByteBuffer buf, GsConnection client) {
		super(buf, client, 0x0A);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void readImpl() {
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	protected void runImpl() {
		GameServerInfo gsi = getConnection().getGameServerInfo();

		Logger.getLogger(getClass()).info("Clearing character counts for " + gsi.getId() + ".");
		
		AccountController.clearCharacterCounts(gsi.getId());
	}
}
