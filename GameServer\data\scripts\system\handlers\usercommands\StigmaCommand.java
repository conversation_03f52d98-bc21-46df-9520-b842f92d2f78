/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ArenaService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;

/**
 * 
 * <AUTHOR>
 */
public class StigmaCommand extends UserCommand {
    private static final Logger log = Logger.getLogger(StigmaCommand.class);

    private static Map<Integer, Long> lastExecute = new HashMap<Integer, Long>();

    public StigmaCommand() {
        super("stigma");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 2000) {
                message(player, "You cannot use this command more than every 2 seconds!");
                return;
            }
        }

        if (params[0].equalsIgnoreCase("save")) {
            if (params.length < 2) {
                message(player, "Syntax: .stigma save <name> -- saves current stigma build");
                return;
            }

            List<Item> stigmas = player.getEquipment().getEquippedItemsStigma();

            if (stigmas.size() < 6) {
                message(player, "You can only save full stigma builds!");
                return;
            }

            String name = params[1].toLowerCase();
            if (name.length() > 16) {
                message(player, "Stigma build names cannot be longer than 16 characters.");
                return;
            }

            if (checkStigmaBuildName(player, name)) {
                message(player,
                    "The stigma build name is already taken. Choose a new one or delete the old.");
                return;
            }

            if (saveStigmaBuild(player, stigmas, name)) {
                message(player, "The stigma build " + name + " has been saved!");
            }
            else {
                message(player, "An error occured while saving the stigma build!");
            }
        }
        else if (params[0].equalsIgnoreCase("delete")) {
            if (params.length < 2) {
                message(player, "Syntax: .stigma delete <name> -- deletes stigma build");
                return;
            }

            String name = params[1].toLowerCase();
            if (name.length() > 16) {
                message(player, "Stigma build names cannot be longer than 16 characters.");
                return;
            }

            if (deleteStigmaBuild(player, name)) {
                message(player, "The stigma build " + name + " has been deleted!");
            }
            else {
                message(player, "An error occured while deleting the stigma build!");
            }
        }
        else if (params[0].equalsIgnoreCase("load")) {
            if (params.length < 2) {
                message(player, "Syntax: .stigma load <name> -- loads stigma build");
                return;
            }

            String name = params[1].toLowerCase();
            if (name.length() > 16) {
                message(player, "Stigma build names cannot be longer than 16 characters.");
                return;
            }

            if (!checkStigmaBuildName(player, name)) {
                message(player, "The stigma build " + name
                    + " does not exist. Type .stigma list to see your stigma builds.");
                return;
            }

            if (player.getInventory().getNumberOfFreeSlots() < 6) {
                message(player, "You need 6 free inventory slots to use this command!");
                return;
            }

            Map<Long, Integer> stigmas = getStigmaBuild(player, name);
            if (stigmas.size() < 6) {
                message(player, "Something went wrong loading the stigma build!");
            }
            else if (player.isInCombatLong()) {
                message(player, "You cannot load stigma builds while in combat!");
            }
            else if (player.isSpectating()) {
                message(player, "You cannot load stigma builds while spectating!");
            }
            else if (ArenaService.getInstance().isInArena(player)) {
                message(player, "You cannot load stigma builds while in FFA!");
            }
            else if (player.getBattleground() != null
                && (player.getBattleground().is1v1() || player.getBattleground().is2v2())) {
                message(player, "You cannot load stigma builds in 1v1/2v2!");
            }
            else if (player.getInventory().getKinahCount() < 250000) {
                message(player, "You need at least 250000 Kinah to load stigmas.");
            }
            else if (isNearNpcTitle(player, 350410) || isNearNpcName(player, 463112)
                || isNearNpcTitle(player, 314362)
                || (player.getBattleground() != null && !player.getBattleground().isStarted())) {
                List<Item> currentStigmas = player.getEquipment().getEquippedItemsStigma();

                for (Item stigma : currentStigmas) {
                    player.getEquipment().unEquipItem(stigma.getObjectId(), true);
                }

                for (Map.Entry<Long, Integer> entry : stigmas.entrySet()) {
                    long slot = entry.getKey();
                    int itemObjectId = entry.getValue();

                    Item stigma = player.getInventory().getItemByObjId(itemObjectId);
                    if (stigma == null) {
                        message(player,
                            "One of the stigma stones from your build are missing. The build is corrupt!");
                        break;
                    }

                    player.getEquipment().equipItem(stigma.getObjectId(), slot);
                }

                message(player, "The stigma build " + name + " has been loaded!");
            }
            else {
                message(
                    player,
                    "You are not near a Stigma Master and not in the staging phase of a BG! You cannot load stigmas now.");
            }
        }
        else if (params[0].equalsIgnoreCase("list")) {
            List<String> builds = getStigmaBuilds(player);

            if (builds.isEmpty()) {
                message(player, "You have no saved stigma builds.");
            }
            else {
                StringBuilder sb = new StringBuilder();

                sb.append("=== Stigma Builds ===");

                for (String name : builds) {
                    sb.append("\n- ").append(name);
                }

                message(player, sb.toString());
            }
        }
        else {
            message(
                player,
                "Syntax: .stigma <save | delete | load | list>"
                    + "\nSyntax: Loading stigma build is only possible at the beginning of a BG or near a Stigma Master.");
            return;
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }

    public static boolean checkStigmaBuildName(Player player, String name) {
        PreparedStatement ps = DB
            .prepareStatement("SELECT id FROM stigma_build WHERE player_id = ? AND name LIKE ?");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setString(2, name);

            ResultSet rs = ps.executeQuery();

            return rs.next();
        }
        catch (Exception e) {
            // log.error("Error checking stigma build name existance for " + player.getName()
            // + " stigma build name " + name, e);
            return false;
        }
        finally {
            DB.close(ps);
        }
    }

    public static boolean saveStigmaBuild(Player player, List<Item> stigmas, String name) {
        Connection con = null;
        PreparedStatement ps = null;

        int buildId = 0;

        try {
            con = DatabaseFactory.getConnection();
            ps = con.prepareStatement("INSERT INTO stigma_build (player_id,name) VALUES (?,?)",
                Statement.RETURN_GENERATED_KEYS);

            ps.setInt(1, player.getObjectId());
            ps.setString(2, name);

            ps.executeUpdate();

            ResultSet rs = ps.getGeneratedKeys();

            if (rs.next())
                buildId = rs.getInt(1);
        }
        catch (Exception e) {
            // log.error("Error inserting stigma build " + name + " for player " + player.getName(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        if (buildId == 0)
            return false;

        for (Item stigma : stigmas) {
            ps = DB
                .prepareStatement("INSERT INTO stigma_build_items (build_id,object_id,slot) VALUES (?,?,?)");

            try {
                ps.setInt(1, buildId);
                ps.setInt(2, stigma.getObjectId());
                ps.setLong(3, stigma.getEquipmentSlot());

                ps.execute();
            }
            catch (Exception e) {
                log.error("Error inserting stigma build item", e);
            }
            finally {
                DB.close(ps);
            }
        }

        return true;
    }

    public static boolean deleteStigmaBuild(Player player, String name) {
        PreparedStatement ps = DB
            .prepareStatement("DELETE FROM stigma_build WHERE player_id = ? AND name LIKE ?");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setString(2, name);

            ps.execute();
        }
        catch (Exception e) {
            log.error("Error deleting stigma build", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    public static Map<Long, Integer> getStigmaBuild(Player player, String name) {
        Map<Long, Integer> stigmas = new HashMap<Long, Integer>();

        PreparedStatement ps = DB
            .prepareStatement("SELECT slot,object_id FROM stigma_build b LEFT JOIN stigma_build_items i ON b.id = i.build_id WHERE player_id = ? AND name LIKE ?");

        try {
            ps.setInt(1, player.getObjectId());
            ps.setString(2, name);

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                stigmas.put(rs.getLong("slot"), rs.getInt("object_id"));
            }
        }
        catch (Exception e) {
            log.error("Error loading stigma build for " + player.getName() + " stigma build name "
                + name, e);
        }
        finally {
            DB.close(ps);
        }

        return stigmas;
    }

    public static List<String> getStigmaBuilds(Player player) {
        List<String> builds = new ArrayList<String>();

        PreparedStatement ps = DB
            .prepareStatement("SELECT name FROM stigma_build WHERE player_id = ?");

        try {
            ps.setInt(1, player.getObjectId());

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                builds.add(rs.getString("name"));
            }
        }
        catch (Exception e) {
            log.error("Error loading stigma builds for " + player.getName(), e);
        }
        finally {
            DB.close(ps);
        }

        return builds;
    }

    private boolean isNearNpcTitle(Player player, int titleId) {
        if (!player.isSpawned())
            return false;

        for (Npc npc : player.getKnownList().getNpcs())
            if (npc.getObjectTemplate().getTitleId() == titleId
                && MathUtil.isInRange(player, npc, 100))
                return true;

        return false;
    }

    private boolean isNearNpcName(Player player, int nameId) {
        if (!player.isSpawned())
            return false;

        for (Npc npc : player.getKnownList().getNpcs())
            if (npc.getObjectTemplate().getNameId() == nameId
                && MathUtil.isInRange(player, npc, 100))
                return true;

        return false;
    }
}